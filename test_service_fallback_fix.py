#!/usr/bin/env python3
"""
Test the specific service fallback fix based on the user's terminal logs.
This test replicates the exact scenario from the logs:
1. Select "Mutation of Land" service
2. Ask service-related question (should work)
3. Ask general questions (should show fallback message)
"""

import requests
import json
import time

def test_mutation_of_land_scenario():
    """Test the exact scenario from user's terminal logs"""
    
    print("🧪 Testing Mutation of Land Service Fallback Fix")
    print("=" * 70)
    print("Replicating the exact scenario from the user's terminal logs:")
    print("- Selected service: '11. Mutation of Land'")
    print("- Test queries: fees, trade licence, silpasathi")
    print("=" * 70)
    
    url = "http://localhost:8020/chatbot/step"
    session_id = "test_mutation_fallback"
    
    try:
        # Setup: Simulate the session state from the logs
        print("\n📋 Setting up session with 'Mutation of Land' service")
        print("-" * 50)
        
        # Step 1: Set main option
        setup1_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "step": 1,
            "user_response": {
                "caption": "main_option",
                "value": "1. Apply for licence/clearance"
            }
        }
        
        response1 = requests.post(url, json=setup1_payload, timeout=30)
        print(f"Main option setup: {response1.status_code}")
        
        time.sleep(1)
        
        # Step 2: Set service type
        setup2_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "step": 2,
            "user_response": {
                "caption": "service_type",
                "value": "1. Pre-establishment"
            }
        }
        
        response2 = requests.post(url, json=setup2_payload, timeout=30)
        print(f"Service type setup: {response2.status_code}")
        
        time.sleep(1)
        
        # Step 3: Set selected service (Mutation of Land)
        setup3_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "step": 3,
            "user_response": {
                "caption": "selected_service",
                "value": "11. Mutation of Land"
            }
        }
        
        response3 = requests.post(url, json=setup3_payload, timeout=30)
        print(f"Selected service setup: {response3.status_code}")
        print("✅ Session setup complete with 'Mutation of Land' service")
        
        time.sleep(2)
        
        # Test 1: Service-related query (should work - from logs this worked)
        print("\n📋 Test 1: Service-related query - 'what is the fees?'")
        print("-" * 50)
        
        test1_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "user_input": "what is the fees?",
            "step": 4,
            "response_type": "text"
        }
        
        response_test1 = requests.post(url, json=test1_payload, timeout=60)
        
        if response_test1.status_code == 200:
            result1 = response_test1.json()
            response_text1 = result1.get('response', '')
            intent_name1 = result1.get('intent_name', '')
            
            print(f"✅ Request successful: {response_test1.status_code}")
            print(f"Intent: {intent_name1}")
            print(f"Response: {response_text1[:100]}...")
            
            if "This query is not related to" in response_text1:
                print("❌ Service-related query incorrectly triggered fallback!")
                test1_success = False
            else:
                print("✅ Service-related query returned proper response")
                test1_success = True
        else:
            print(f"❌ Test 1 failed: {response_test1.status_code}")
            test1_success = False
        
        time.sleep(2)
        
        # Test 2: General query about trade licence (should trigger fallback)
        print("\n📋 Test 2: General query - 'what is the trade licence apply process?'")
        print("-" * 50)
        
        test2_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "user_input": "what is the trade licence apply process?",
            "step": 4,
            "response_type": "text"
        }
        
        response_test2 = requests.post(url, json=test2_payload, timeout=60)
        
        if response_test2.status_code == 200:
            result2 = response_test2.json()
            response_text2 = result2.get('response', '')
            intent_name2 = result2.get('intent_name', '')
            
            print(f"✅ Request successful: {response_test2.status_code}")
            print(f"Intent: {intent_name2}")
            print(f"Response: {response_text2[:150]}...")
            
            if "This query is not related to" in response_text2 and "11. Mutation of Land" in response_text2:
                print("✅ General query correctly triggered service fallback!")
                test2_success = True
            else:
                print("❌ General query did not trigger fallback (this was the issue)")
                test2_success = False
        else:
            print(f"❌ Test 2 failed: {response_test2.status_code}")
            test2_success = False
        
        time.sleep(2)
        
        # Test 3: General query about Silpasathi (should trigger fallback)
        print("\n📋 Test 3: General query - 'what is silpasathi?'")
        print("-" * 50)
        
        test3_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "user_input": "what is silpasathi?",
            "step": 4,
            "response_type": "text"
        }
        
        response_test3 = requests.post(url, json=test3_payload, timeout=60)
        
        if response_test3.status_code == 200:
            result3 = response_test3.json()
            response_text3 = result3.get('response', '')
            intent_name3 = result3.get('intent_name', '')
            
            print(f"✅ Request successful: {response_test3.status_code}")
            print(f"Intent: {intent_name3}")
            print(f"Response: {response_text3[:150]}...")
            
            if "This query is not related to" in response_text3 and "11. Mutation of Land" in response_text3:
                print("✅ Silpasathi query correctly triggered service fallback!")
                test3_success = True
            else:
                print("❌ Silpasathi query did not trigger fallback (this was the main issue)")
                test3_success = False
        else:
            print(f"❌ Test 3 failed: {response_test3.status_code}")
            test3_success = False
        
        # Final assessment
        print("\n" + "=" * 70)
        print("🎯 FINAL TEST RESULTS")
        print("=" * 70)
        
        print(f"✅ Service-related query (fees): {'PASS' if test1_success else 'FAIL'}")
        print(f"✅ General query (trade licence): {'PASS' if test2_success else 'FAIL'}")
        print(f"✅ General query (silpasathi): {'PASS' if test3_success else 'FAIL'}")
        
        overall_success = test1_success and test2_success and test3_success
        
        if overall_success:
            print("\n🎉 SERVICE FALLBACK FIX: ✅ SUCCESSFUL!")
            print("✅ Service-related queries return proper responses")
            print("✅ General queries now trigger service fallback message")
            print("✅ User's terminal log issue has been resolved!")
            
        elif test1_success and not (test2_success and test3_success):
            print("\n⚠️  PARTIAL SUCCESS")
            print("✅ Service-related queries working")
            print("❌ General queries still not triggering fallback")
            print("The fix needs further investigation")
            
        else:
            print("\n❌ FIX NOT WORKING")
            print("The service fallback logic still has issues")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Run the targeted service fallback test"""
    
    print("Service Fallback Fix Test - Based on User's Terminal Logs")
    print("=" * 70)
    print("This test addresses the specific issue from the user's logs:")
    print("- Service: 'Mutation of Land'")
    print("- Problem: General queries returning Milvus responses instead of fallback")
    print("- Expected: General queries should show service fallback message")
    print("=" * 70)
    
    # Wait for server to be ready
    print("⏳ Waiting 3 seconds for server to be ready...")
    time.sleep(3)
    
    success = test_mutation_of_land_scenario()
    
    if success:
        print("\n🎉 SUCCESS: The service fallback fix is working correctly!")
        print("Users will now see the correct fallback message for non-service queries.")
    else:
        print("\n⚠️  ISSUE: The service fallback fix needs further investigation.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
