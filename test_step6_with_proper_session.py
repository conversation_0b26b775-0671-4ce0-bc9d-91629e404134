#!/usr/bin/env python3
"""
Test Step 6 RAG fallback with proper session setup.
This test properly sets up the session state to match the user's scenario.
"""

import requests
import json
import time

def setup_step6_session(session_id):
    """Set up session state to match the user's log scenario"""
    
    print("🔧 Setting up Step 6 session state...")
    
    url = "http://localhost:8020/chatbot/step"
    
    # Step 1: Set main option to "Know application status"
    payload1 = {
        "session_id": session_id,
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "step": 1,
        "user_response": {
            "caption": "main_option",
            "value": "2. Know application status"
        }
    }
    
    try:
        response1 = requests.post(url, json=payload1, timeout=30)
        print(f"Step 1 setup: {response1.status_code}")
        
        # Step 2: Simulate CAF number entry (optional for our test)
        # We'll just manually set the session data we need
        
        # For testing purposes, we'll make a direct call to simulate the session state
        # that would exist after completing the status check flow
        
        return True
        
    except Exception as e:
        print(f"Session setup error: {e}")
        return False

def test_step6_with_session_setup():
    """Test Step 6 RAG fallback with proper session setup"""
    
    print("🧪 Testing Step 6 RAG Fallback with Proper Session Setup")
    print("=" * 70)
    
    session_id = "test_step6_proper_session"
    
    # Setup session
    if not setup_step6_session(session_id):
        print("❌ Session setup failed")
        return False
    
    # Wait a moment
    time.sleep(2)
    
    # Now test the Step 6 query with a different approach
    # Let's try to simulate the exact conditions from the user's log
    
    url = "http://localhost:8020/chatbot/step"
    
    # Create a payload that matches the user's log exactly
    payload = {
        "session_id": session_id,
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "user_input": "what is kanyashree?",
        "step": 6,
        "response_type": "text"
    }
    
    print(f"Sending Step 6 query: '{payload['user_input']}'")
    print("Expected: Should trigger RAG fallback and return response with disclaimer")
    print("-" * 60)
    
    try:
        response = requests.post(url, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            intent_name = result.get('intent_name', '')
            
            print(f"✅ Request successful!")
            print(f"Status: {response.status_code}")
            print(f"Intent: {intent_name}")
            print(f"Response length: {len(response_text)} chars")
            print(f"Response preview: {response_text[:150]}...")
            
            # Check for RAG response indicators
            if 'Disclaimer: This is an AI generated Response' in response_text:
                print("✅ Step 6 RAG fallback working! (Disclaimer found)")
                return True
            elif 'Thank you for your query!' in response_text:
                print("❌ Step 6 still showing fallback message")
                return False
            elif 'Silpasathi is the Single Window Portal' in response_text:
                print("⚠️  Step 6 returning Milvus response (not RAG fallback)")
                print("This suggests the fallback condition is not being triggered")
                return False
            else:
                print("⚠️  Step 6 unclear result")
                return False
                
        else:
            print(f"❌ Step 6 request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Step 6 error: {e}")
        return False

def test_step6_direct_approach():
    """Test Step 6 by directly calling with the conditions that should work"""
    
    print("\n🧪 Testing Step 6 Direct Approach (Bypass Session Requirements)")
    print("=" * 70)
    
    # Let's test if we can trigger the Step 6 handler by examining the code logic
    # From the logs, it seems like the Step 6 handler isn't being reached
    
    # Let's try a different approach - test with a session that has the required data
    url = "http://localhost:8020/chatbot/step"
    
    # Create a session ID and try to set it up with the required session data
    session_id = "test_step6_direct"
    
    # First, let's try to understand what session data is needed by looking at the handler condition:
    # if req.step == 6 and main_option == "2. Know application status" and get_session_data(req.session_id, "status_check_completed"):
    
    # We need:
    # 1. step = 6 ✓
    # 2. main_option = "2. Know application status" 
    # 3. status_check_completed = True
    
    # Let's try to create this state by making a series of requests
    
    print("Attempting to create proper session state...")
    
    # Step 1: Set main option
    payload1 = {
        "session_id": session_id,
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "step": 1,
        "user_response": {
            "caption": "main_option", 
            "value": "2. Know application status"
        }
    }
    
    try:
        response1 = requests.post(url, json=payload1, timeout=30)
        print(f"Main option setup: {response1.status_code}")
        
        if response1.status_code == 200:
            result1 = response1.json()
            print(f"Response: {result1.get('response', '')[:100]}...")
        
        # Now try the Step 6 query
        payload6 = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "user_input": "what is kanyashree?",
            "step": 6,
            "response_type": "text"
        }
        
        print(f"\nTesting Step 6 query...")
        response6 = requests.post(url, json=payload6, timeout=120)
        
        if response6.status_code == 200:
            result6 = response6.json()
            response_text = result6.get('response', '')
            intent_name = result6.get('intent_name', '')
            
            print(f"✅ Step 6 request successful!")
            print(f"Intent: {intent_name}")
            print(f"Response: {response_text[:200]}...")
            
            if 'Disclaimer: This is an AI generated Response' in response_text:
                print("✅ Step 6 RAG fallback working!")
                return True
            else:
                print("❌ Step 6 RAG fallback not triggered")
                return False
        else:
            print(f"❌ Step 6 request failed: {response6.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Direct approach error: {e}")
        return False

if __name__ == "__main__":
    print("Step 6 RAG Fallback Test with Proper Session Setup")
    print("=" * 70)
    
    # Wait for server
    print("⏳ Waiting 2 seconds for server...")
    time.sleep(2)
    
    # Test 1: With session setup
    success1 = test_step6_with_session_setup()
    
    # Test 2: Direct approach
    success2 = test_step6_direct_approach()
    
    print("\n" + "=" * 70)
    print("FINAL RESULTS")
    print("=" * 70)
    
    print(f"Test 1 (Session Setup): {'PASS' if success1 else 'FAIL'}")
    print(f"Test 2 (Direct Approach): {'PASS' if success2 else 'FAIL'}")
    
    if success1 or success2:
        print("\n✅ Step 6 RAG fallback is working!")
    else:
        print("\n❌ Step 6 RAG fallback needs more investigation")
        print("\nPossible issues:")
        print("1. Session state requirements not met")
        print("2. Step 6 handler conditions not satisfied")
        print("3. Different handler being triggered instead")
    
    print("\n📝 Next steps:")
    print("1. Check server logs for handler routing")
    print("2. Verify session data requirements")
    print("3. Test with actual user flow simulation")
