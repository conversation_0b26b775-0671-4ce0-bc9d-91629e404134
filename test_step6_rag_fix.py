#!/usr/bin/env python3
"""
Test script to verify that Step 6 RAG fallback is now working correctly.
This simulates the post-status check general query scenario.
"""

import requests
import json

def test_step6_rag_fallback():
    """Test that Step 6 RAG API is called when <PERSON><PERSON><PERSON><PERSON> returns fallback"""
    
    # API endpoint
    url = "http://localhost:8020/chatbot/step"
    
    # First, simulate the session setup for post-status check
    session_id = "test_step6_session"
    
    # Test payload for Step 6 - post-status general query
    payload = {
        "session_id": session_id,
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "user_input": "what is kanyashree?",  # This query from the log that triggered fallback
        "step": 6,
        "response_type": "text"
    }
    
    print("Testing Step 6 RAG fallback functionality...")
    print(f"Sending query: '{payload['user_input']}'")
    print("Expected behavior: <PERSON><PERSON><PERSON><PERSON> should return fallback, then RAG API should be called")
    print("-" * 60)
    
    try:
        # First, we need to set up the session to simulate post-status check state
        # This would normally be done through the full flow, but for testing we'll call directly
        
        # Send the Step 6 request
        response = requests.post(url, json=payload, timeout=90)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"Response status code: {response.status_code}")
            print(f"Intent name: {result.get('intent_name', 'N/A')}")
            print(f"Response length: {len(result.get('response', ''))}")
            print(f"Response preview: {result.get('response', '')[:200]}...")
            
            # Check if RAG was used (look for disclaimer or longer response)
            response_text = result.get('response', '')
            if 'Disclaimer: This is an AI generated Response' in response_text:
                print("✅ RAG API was successfully called! (Disclaimer found)")
                return True
            elif len(response_text) > 100 and 'Thank you for your query!' not in response_text:
                print("✅ RAG API likely called (longer response without fallback message)")
                return True
            elif 'Thank you for your query! Could you please clarify' in response_text:
                print("❌ Still getting fallback message - RAG API not called")
                return False
            else:
                print("⚠️  Unclear if RAG was called - response doesn't match expected patterns")
                print(f"Full response: {response_text}")
                return False
                
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def setup_session_for_step6():
    """Helper function to set up session state for Step 6 testing"""
    url = "http://localhost:8020/chatbot/step"
    session_id = "test_step6_session"
    
    # This is a simplified setup - in real flow, user would go through:
    # 1. Select "Know application status"
    # 2. Enter CAF number
    # 3. Enter OTP
    # 4. See status
    # 5. Then ask general queries (Step 6)
    
    print("Setting up session for Step 6 testing...")
    
    # We'll try to simulate the minimal session state needed
    setup_payload = {
        "session_id": session_id,
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "step": 1,
        "user_response": {
            "caption": "main_option",
            "value": "2. Know application status"
        }
    }
    
    try:
        response = requests.post(url, json=setup_payload, timeout=30)
        if response.status_code == 200:
            print("✅ Session setup successful")
            return True
        else:
            print(f"⚠️  Session setup returned {response.status_code}, continuing anyway...")
            return True  # Continue with test even if setup fails
    except Exception as e:
        print(f"⚠️  Session setup failed: {e}, continuing anyway...")
        return True  # Continue with test even if setup fails

if __name__ == "__main__":
    print("Step 6 RAG Fallback Fix Test")
    print("=" * 60)
    
    # Wait a moment for server to be ready
    print("Waiting 2 seconds for server to be ready...")
    import time
    time.sleep(2)
    
    # Set up session (optional, test might work without it)
    setup_session_for_step6()
    
    # Run the main test
    success = test_step6_rag_fallback()
    
    if success:
        print("\n🎉 Step 6 RAG fallback fix is working correctly!")
    else:
        print("\n❌ Step 6 RAG fallback fix needs more investigation.")
        print("Check the server logs for more details.")
