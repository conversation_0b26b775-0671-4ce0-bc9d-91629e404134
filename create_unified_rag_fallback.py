#!/usr/bin/env python3
"""
<PERSON>ript to create a unified RAG fallback function and update main.py to use it.
This will consolidate all the duplicate RAG API calling logic.
"""

def create_unified_rag_function():
    """Create the unified RAG fallback function code"""
    
    unified_function = '''
def unified_rag_fallback(user_query: str, context_info: str = ""):
    """
    Unified RAG fallback function to handle all RAG API calls consistently.
    
    Args:
        user_query (str): The user's query to send to RAG API
        context_info (str): Optional context information for logging
    
    Returns:
        dict: Updated result with RAG response or fallback message
    """
    try:
        logger.info(f"Unified RAG fallback called for query: '{user_query}' {context_info}")
        
        # Load RAG configuration
        link_data = json_load_rag()
        
        # Call RAG API
        rag_result = rag_api(link_data=link_data["silpasathi_links"]["link"], question=user_query)
        logger.info(f"RAG API response received: {rag_result}")
        
        # Process RAG response
        if rag_result.get('answer') and len(rag_result['answer']) > 0:
            if len(rag_result['answer']) > 1:
                response_text = f"{rag_result['answer'][0]}<br><br><code>{rag_result['answer'][1]}</code><br><br><b>Disclaimer: This is an AI generated Response. Please verify it from authentic sources.</b>"
            else:
                response_text = f"{rag_result['answer'][0]}<br><br><b>Disclaimer: This is an AI generated Response. Please verify it from authentic sources.</b>"
            
            logger.info(f"RAG API provided useful response {context_info}")
            return {
                "success": True,
                "response": response_text,
                "intent_name": "rag_response",
                "source": "rag_api"
            }
        else:
            logger.info(f"RAG API didn't provide useful response {context_info}")
            return {
                "success": False,
                "response": "I couldn't find specific information about your query. Please try rephrasing your question or contact support for assistance.",
                "intent_name": "rag_fallback_failed",
                "source": "default_fallback"
            }
            
    except Exception as rag_error:
        logger.error(f"RAG API error {context_info}: {rag_error}")
        return {
            "success": False,
            "response": "I couldn't find specific information about your query. Please try rephrasing your question or contact support for assistance.",
            "intent_name": "rag_error",
            "source": "error_fallback"
        }

def should_trigger_rag_fallback(result: dict) -> bool:
    """
    Check if a chatbot_response result should trigger RAG fallback.
    
    Args:
        result (dict): Result from chatbot_response function
    
    Returns:
        bool: True if RAG fallback should be triggered
    """
    return (
        result.get("intent_name") == "fallback" or
        not result.get("response") or
        result.get("response") == "Thank you for your query! Could you please clarify or provide more details so that I can assist you effectively?"
    )
'''
    
    return unified_function

def show_locations_to_update():
    """Show all the locations in main.py that need to be updated to use unified function"""
    
    locations = [
        {
            "location": "Step 1 direct general query handler",
            "line_range": "~1530-1550",
            "current_pattern": "rag_res = rag_api(link_data=link_data[\"silpasathi_links\"][\"link\"], question=user_input)"
        },
        {
            "location": "Step 6 post-status query handler", 
            "line_range": "~1649-1670",
            "current_pattern": "rag_result = rag_api(link_data=link_data[\"silpasathi_links\"][\"link\"], question=query_with_context)"
        },
        {
            "location": "handle_general_query_after_status_fallback function",
            "line_range": "~725-740", 
            "current_pattern": "rag_res = rag_api(link_data=link_data[\"silpasathi_links\"][\"link\"], question=req.user_input)"
        },
        {
            "location": "handle_direct_general_query_fallback function",
            "line_range": "~836-850",
            "current_pattern": "rag_res = rag_api(link_data=link_data[\"silpasathi_links\"][\"link\"], question=req.user_input)"
        },
        {
            "location": "Default fallback section",
            "line_range": "~2258-2270",
            "current_pattern": "rag_res = rag_api(link_data=link_data[\"silpasathi_links\"][\"link\"], question=req.user_input)"
        },
        {
            "location": "Step 8 handler",
            "line_range": "~2837-2850",
            "current_pattern": "rag_res = rag_api(link_data=link_data[\"silpasathi_links\"][\"link\"], question=req.user_input)"
        }
    ]
    
    return locations

if __name__ == "__main__":
    print("Unified RAG Fallback Function Creation")
    print("=" * 50)
    
    print("\n1. UNIFIED FUNCTION CODE:")
    print("-" * 30)
    print(create_unified_rag_function())
    
    print("\n2. LOCATIONS TO UPDATE:")
    print("-" * 30)
    locations = show_locations_to_update()
    for i, loc in enumerate(locations, 1):
        print(f"{i}. {loc['location']}")
        print(f"   Line range: {loc['line_range']}")
        print(f"   Pattern: {loc['current_pattern']}")
        print()
    
    print("3. BENEFITS OF UNIFIED APPROACH:")
    print("-" * 30)
    print("✅ Consistent RAG API calling across all handlers")
    print("✅ Centralized error handling and logging")
    print("✅ Easier maintenance and debugging")
    print("✅ Consistent response formatting")
    print("✅ Reduced code duplication")
    
    print("\n4. NEXT STEPS:")
    print("-" * 30)
    print("1. Add unified function to main.py")
    print("2. Update all 6 locations to use unified function")
    print("3. Test each handler to ensure RAG fallback works")
    print("4. Remove duplicate code")
