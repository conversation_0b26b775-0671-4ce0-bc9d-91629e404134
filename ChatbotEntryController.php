<?php
class ChatbotEntryController extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->helper(array('form_helper', 'url_helper', 'file_helper'));
        $this->load->library('form_validation', 'auth');
        $this->load->model("super_admin/ChatbotEntryModel");
        $this->admin = $this->session->userdata('admin');
    }

/*******************************************EoDB chatbot******************** */

    //AI Sanlaap (Original method preserved)
    public function aiSanlaapEODB()
    {
        $this->load->library(array('auth'));
        $data['admin'] = $this->admin;

        // Get configuration from backend
        $data['chatbot_config'] = $this->getChatbotConfig();

        $this->load->view('super_admin/sanlaap_bot_eodb_view', $data);
    }

    /**
     * Get chatbot configuration from Python backend
     */
    public function getChatbotConfig()
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://localhost:8020/chatbot/config',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($httpCode === 200) {
            $config = json_decode($response, true);
            return $config['success'] ? $config['config'] : $this->getDefaultConfig();
        }

        return $this->getDefaultConfig();
    }

    /**
     * Get default configuration if backend is not available
     * Note: Messages are stored in database, this only provides structural fallbacks
     */
    private function getDefaultConfig()
    {
        return array(
            'chatbot_name' => 'AI Sanlaap',
            'greeting' => array(), // Messages come from database, not hardcoded
            'api_endpoints' => array(
                'python_api_url' => 'http://localhost:8020/chatbot/step',
                'python_session_create_url' => 'http://localhost:8020/session_manager',
                'session_create_url' => base_url('super_admin/ChatbotEntryController/sessionCreateEODB'),
                'feedback_url' => base_url('super_admin/ChatbotEntryController/chatFeedbackEODB'),
                'form_submit_url' => base_url('super_admin/ChatbotEntryController/chatFormSubmitEODB')
            ),
            'collection_name' => 'collection_b5e7c017_1a06_4857_819c_6a038133dd94',
            'keywords' => array(
                'application_status' => array('application status', 'status'),
                'licence' => array('licence', 'clearance', 'apply'),
                'exit' => array('exit', 'quit', 'restart', 'reset')
            )
        );
    }

    /**
     * Create session with welcome message from Python backend
     */
    public function createSessionWithWelcome()
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://localhost:8020/session_manager',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode(array(
                'session_type' => 'session_create',
                'session_id' => null
            )),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curl);
        curl_close($curl);

        // Log for debugging
        error_log("createSessionWithWelcome - HTTP Code: " . $httpCode);
        error_log("createSessionWithWelcome - Response: " . $response);

        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);
            if ($data && isset($data['session_id'])) {
                // Convert session_manager response format to frontend expected format
                $formatted_response = array(
                    'session_id' => $data['session_id'],
                    'intent_id' => '100',
                    'intent_name' => 'session_created',
                    'response' => $data['welcome_message'],
                    'response_type' => 'text',
                    'option_list' => 'NA',
                    'followup_yes' => 'NA',
                    'followup_no' => 'NA',
                    'step' => 1
                );
                echo json_encode($formatted_response);
            } else {
                error_log("createSessionWithWelcome - Invalid response format: " . $response);
                // Fallback response
                echo json_encode(array(
                    'session_id' => 'session_' . time() . '_' . rand(1000, 9999),
                    'intent_id' => '100',
                    'intent_name' => 'session_created',
                    'response' => 'Welcome! You can use the menu options above or type your query directly in the text box below.',
                    'response_type' => 'text',
                    'option_list' => 'NA',
                    'followup_yes' => 'NA',
                    'followup_no' => 'NA',
                    'step' => 1
                ));
            }
        } else {
            error_log("createSessionWithWelcome - Backend error. HTTP Code: " . $httpCode . ", CURL Error: " . $curlError);
            // Fallback response
            echo json_encode(array(
                'session_id' => 'session_' . time() . '_' . rand(1000, 9999),
                'intent_id' => '100',
                'intent_name' => 'session_created',
                'response' => 'Welcome! You can use the menu options above or type your query directly in the text box below.',
                'response_type' => 'text',
                'option_list' => 'NA',
                'followup_yes' => 'NA',
                'followup_no' => 'NA',
                'step' => 1
            ));
        }
    }

    /**
     * Handle Python API communication for chatbot queries
     */
    public function pythonApiProxy()
    {
        // Get POST data
        $input = json_decode(file_get_contents('php://input'), true);

        // Log the incoming request for debugging
        error_log("Python API Proxy - Input: " . json_encode($input));

        if (!$input) {
            error_log("Python API Proxy - Invalid input data");
            echo json_encode(array(
                'error' => 'Invalid input data',
                'response' => 'An error occurred. Please try again.',
                'response_type' => 'text'
            ));
            return;
        }

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://localhost:8020/chatbot/step',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($input),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curl);
        curl_close($curl);

        // Log the response for debugging
        error_log("Python API Proxy - HTTP Code: " . $httpCode);
        error_log("Python API Proxy - Response: " . $response);
        if ($curlError) {
            error_log("Python API Proxy - CURL Error: " . $curlError);
        }

        if ($httpCode === 200 && $response) {
            echo $response;
        } else {
            // Log the error
            error_log("Python API Proxy - Backend error. HTTP Code: " . $httpCode . ", CURL Error: " . $curlError);

            // Fallback error response
            echo json_encode(array(
                'session_id' => isset($input['session_id']) ? $input['session_id'] : 'error_session',
                'intent_id' => '500',
                'intent_name' => 'error',
                'response' => 'Backend connection error. Please try again.',
                'response_type' => 'text',
                'option_list' => 'NA',
                'followup_yes' => 'NA',
                'followup_no' => 'NA',
                'step' => isset($input['step']) ? $input['step'] : 1
            ));
        }
    }

    /**
     * Handle session management operations
     */
    public function sessionManager()
    {
        $operation = $this->input->post('operation');

        switch($operation) {
            case 'create_with_welcome':
                $this->createSessionWithWelcome();
                break;
            case 'destroy':
                $this->sessionDestroyEODB();
                break;
            default:
                echo json_encode(array(
                    'error' => 'Invalid operation',
                    'response' => 'Invalid session operation',
                    'response_type' => 'text'
                ));
        }
    }

    /**
     * Get configuration data for frontend
     */
    public function getConfigData()
    {
        header('Content-Type: application/json');
        echo json_encode($this->getChatbotConfig());
    }

    public function chatFormSubmitEODB()
    {
        //echo"From chatFormSubmit";exit;
        //$name = $this->input->post('name');
        $dob = $this->input->post('dob');
        $gender = $this->input->post('gender');
        $mobile = $this->input->post('mobile');
        $district = $this->input->post('district');
        $block = $this->input->post('block');
        $gp = $this->input->post('gp');
        $village = $this->input->post('village');
        $criteria_list = array(
            'dob' => $dob,
            'gender' => $gender,
            'mobile' => $mobile,
            'district' => $district,
            'block' => $block,
            'gp' => $gp,
            'village' => $village
        );
        $this->session->set_userdata('criteria_list', $criteria_list);
        //echo"<pre>";print_r($this->session->userdata('criteria_list'));
        curl_close($curl);
        echo $response;
    }

    public function sessionCreateEODB()
    {
        // Get collection name from frontend request (required)
        $collection_name = $this->input->post('collection_name');

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://localhost:8020/session_manager',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode(array(
                'session_type' => 'session_create',
                'session_id' => null
            )),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($httpCode === 200) {
            $res = json_decode($response, true);
            $this->session->set_userdata('session_id', $res['session_id']);
            $this->session->set_userdata('step', 1);

            // Return response to frontend
            echo json_encode(array(
                'session_id' => $res['session_id'],
                'status' => $res['status'],
                'welcome_message' => $res['welcome_message']
            ));
        } else {
            // Fallback response
            echo json_encode(array(
                'session_id' => 'session_' . time() . '_' . rand(1000, 9999),
                'status' => 'Session Created!',
                'welcome_message' => 'Welcome! You can use the menu options above or type your query directly in the text box below.'
            ));
        }
    }

    public function sessionDestroyEODB()
    {
        if ($this->session->has_userdata('step')) {
            $this->session->unset_userdata('step');

        }
        if ($this->session->has_userdata('session_flag')) {
            $this->session->unset_userdata('session_flag');

        }
        if ($this->session->has_userdata('caf_flow')) {
            $this->session->unset_userdata('caf_flow');

        }
        //echo"destroy";exit;
        //echo"<pre>";print_r($_SESSION);exit;
        $session_id = $this->session->userdata('session_id');
        if ($this->session->has_userdata('session_id')) {
            $this->session->unset_userdata('session_id');

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'http://localhost:8020/session_manager',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array(
                    'session_type' => 'session_destroy',
                    'session_id' => $session_id
                )),
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            //echo "Session=".$_SESSION['session_data']['session_id'];exit;

        }
    }

    public function aiSanlaapBotQuestionAnswerEODB()
    {
        // Get collection name from frontend request (required)
        $collection_name = $this->input->post('collection_name');

        $question = $this->input->post('question');
        $language = $this->input->post('language');
        $language = "1";
        $followup_yes = "";
        $followup_no = "";
        $caption = "";
        $value = "";
        $step_id = "";
        $next_step = 1;
        $user_input = "";

        $session_id = "";
        if ($this->session->has_userdata('session_id')) {
            $session_id = $this->session->userdata('session_id');
        }

        if ($this->session->has_userdata('step')) {
            $step_id = $this->session->userdata('step');
        } else {
            $step_id = 1;
            $this->session->set_userdata('step', 1);
        }

        // Process the step
        if (true) { // Always process since we now have a step_id
            // Special handling for application status flow - EXACT MATCH ONLY
            $question_lower = strtolower(trim($question));
            if ($question_lower == "application status" ||
                $question_lower == "status" ||
                $question == "Know application status" ||
                $question == "2. Know application status") {
                $caption = "application_type";
                $value = "2. Know application status";
                $next_step = 5; // Skip to CAF flow
                $user_input = "";
                $this->session->set_userdata('caf_flow', true);
            }
            elseif ($question == "Previous Menu") {
                $caption = "main_option";
                $value = $question;
                $next_step = 1;
                $user_input = "";
            }
            elseif ($question == "Main Menu") {
                $caption = "";
                $value = "";
                $next_step = 1;
                $user_input = "";
                // Clear session flags
                $this->session->unset_userdata('session_flag');
                $this->session->unset_userdata('caf_flow');
            }
            elseif($step_id==1){
                // User has selected a main option
                $caption = "main_option";
                $value = $question;
                $next_step = 2;
                $user_input = "";  // Don't send button text as user_input for option selections
            }
            elseif($step_id==2){
                $caption = "service_type";
                $value = $question;
                $next_step = 3;
                $user_input = "";  // Don't send button text as user_input for option selections
            }
            elseif($step_id==3 && $question=="Do you want to ask general queries?"){
                $caption="application_type";
                $value=$question;
                $next_step=4;
                $user_input="";
                $this->session->set_userdata('session_flag', "Do you want to ask general queries?");
            }
            elseif($step_id==3){
                $caption="application_type";
                $value=$question;
                $next_step=4;
                $user_input="";  // Don't send button text as user_input for option selections
            }
            elseif($step_id==4 && $this->session->has_userdata('session_flag') && $question != "Pre-operational" && $question != "Pre-establishment"){
                $caption="general_query";
                $value="";  // Clear value since we're sending the query as user_input
                $next_step=4;  // Keep the same step for follow-up questions
                $user_input=$question;  // Send the actual user query as user_input for AI processing
                // Keep session_flag for follow-up questions - don't unset it immediately
            }
            elseif($step_id==4 && $question=="Pre-operational"){
                $caption="service_type";
                $value=$question;
                $next_step=5;
                $user_input="";  // Don't send button text as user_input for option selections
            }
            elseif($step_id==4 && $question=="Pre-establishment"){
                $caption="service_type";
                $value=$question;
                $next_step=5;
                $user_input="";  // Don't send button text as user_input for option selections
            }
            elseif($step_id==5){
                // After selecting a specific service, move to step 6 for text input
                $caption="selected_service";
                $value=$question;
                $next_step=6;
                $user_input="";
            }
            elseif($step_id==6){
                // Handle service-specific queries with AI
                $caption="service_query";
                $value="";
                $next_step=6; // Stay in step 6 for follow-up questions
                $user_input=$question;
            }
            // CAF Flow handling
            elseif($step_id==5 && $this->session->has_userdata('caf_flow')){
                // CAF number input
                $caption="caf_number";
                $value="";
                $next_step=6;
                $user_input=$question;
            }
            elseif($step_id==6 && $this->session->has_userdata('caf_flow')){
                // OTP input
                $caption="otp";
                $value="";
                $next_step=7;
                $user_input=$question;
            }
            elseif($step_id==7 && $this->session->has_userdata('caf_flow')){
                // Service selection
                $caption="service_selection";
                $value=$question;
                $next_step=8;
                $user_input="";
            }
            elseif($step_id==8 && $this->session->has_userdata('caf_flow')){
                // Final step - status shown
                $caption="status_complete";
                $value="";
                $next_step=8;
                $user_input="";
                // Clear CAF flow after completion
                $this->session->unset_userdata('caf_flow');
            }
            // Fallback: If user is in step 4 but doesn't match any specific condition, treat as general query
            elseif($step_id==4 && !empty($question)){
                $caption="general_query";
                $value="";
                $next_step=4;
                $user_input=$question;
                // Set session flag to ensure proper handling
                $this->session->set_userdata('session_flag', "Do you want to ask general queries?");
            }
            // Fallback: If user is in step 6 but doesn't match any specific condition, treat as service query
            elseif($step_id==6 && !empty($question)){
                $caption="service_query";
                $value="";
                $next_step=6;
                $user_input=$question;
                // Set session flag for service queries
                $this->session->set_userdata('session_flag', "service_query");
            }
        }

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => 'http://localhost:8020/chatbot/step',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
        "session_id": "'.$session_id.'",
        "collection_name":"'.$collection_name.'",
        "user_input": "'.$user_input.'",
        "step": '.$next_step.',
        "user_response": {
            "caption": "'.$caption.'",
            "value": "'.$value.'"
        },
        "response_type": "",
        "followup_yes": "",
        "followup_no": "",
        "criteria_list": []
        }',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $res = json_decode($response, true);
        if (isset($res['step'])) {
            $this->session->set_userdata('step', $res['step']);
        }
        // Set session flags based on intent
        if($res['intent_name']=="ai_response" || $res['intent_name']=="general_query_prompt"){
            $this->session->set_userdata('session_flag', "Do you want to ask general queries?");
        } elseif($res['intent_name']=="service_query_prompt" || $res['intent_name']=="ai_response"){
            // Set flag for service-specific queries
            $this->session->set_userdata('session_flag', "service_query");
        } elseif($res['intent_name']=="choose_main_option" || $res['intent_name']=="choose_action" || $res['intent_name']=="choose_application_type" || $res['intent_name']=="choose_pre_establishment" || $res['intent_name']=="choose_pre_operation"){
            // Clear the flag only for non-query flows
            $this->session->unset_userdata('session_flag');
        }
        // Keep session_flag for general queries to allow follow-up questions
        echo $response;
    }

    public function schemeBucketEODB()
    {
        $question = $this->input->post('scheme_bucket');
        $intent_name = $this->session->userdata('intent_name');
        $session_id = $this->session->userdata('session_id');
        $step_id = $this->session->userdata('step');
        $criteria_list = $this->session->userdata('criteria_list');

        // Get collection name from frontend request (required)
        $collection_name = $this->input->post('collection_name');

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://localhost:8020/chatbot/step',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
            "session_id": "' . $session_id . '",
            "collection_name":"' . $collection_name . '",
            "user_input": "' . $question . '",
            "step": ' . $step_id . ',
            "user_response": {
                "caption": "scheme_bucket",
                "value": "' . $question . '"
            },
            "response_type": "",
            "followup_yes": "",
            "followup_no": "",
            "criteria_list": ' . json_encode($criteria_list) . '
            }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        echo $response;
    }
}
