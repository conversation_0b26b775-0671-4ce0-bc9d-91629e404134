#!/usr/bin/env python3
"""
Test script to simulate the exact API calls that are causing the followup intent issue.
This will help debug the "fees" -> "mutation of land" flow.
"""

import requests
import json
import time

# Configuration
API_BASE_URL = "http://localhost:8000"  # Adjust if your backend runs on different port
COLLECTION_NAME = "collection_b5e7c017_1a06_4857_819c_6a038133dd94"  # Adjust to your collection

def test_followup_flow():
    """Test the complete followup flow"""
    
    print("=== Testing Followup Flow ===")
    
    # Step 1: Send "fees" query
    print("\n1. Sending 'fees' query...")
    
    request_data = {
        "session_id": "test_session_followup_123",
        "user_input": "fees",
        "step": 1,
        "response_type": "text",
        "collection_name": COLLECTION_NAME
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/chat",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   Response: {json.dumps(result, indent=2)}")
            
            # Check if followup intent was triggered
            if result.get("response") and "service name" in result.get("response", "").lower():
                print("   ✓ Followup intent triggered correctly")
                
                # Step 2: Send "mutation of land" query
                print("\n2. Sending 'mutation of land' query...")
                
                request_data2 = {
                    "session_id": "test_session_followup_123",  # Same session ID
                    "user_input": "mutation of land",
                    "step": result.get("step", 1),  # Use step from previous response
                    "response_type": "text",
                    "collection_name": COLLECTION_NAME
                }
                
                response2 = requests.post(
                    f"{API_BASE_URL}/chat",
                    json=request_data2,
                    headers={"Content-Type": "application/json"}
                )
                
                if response2.status_code == 200:
                    result2 = response2.json()
                    print(f"   Response: {json.dumps(result2, indent=2)}")
                    
                    # Check if we got fees information (not timeline)
                    response_text = result2.get("response", "").lower()
                    if "fees" in response_text or "fee" in response_text or "cost" in response_text or "charge" in response_text:
                        print("   ✓ Correct fees response received")
                    elif "days" in response_text or "timeline" in response_text or "time" in response_text:
                        print("   ✗ ERROR: Got timeline response instead of fees!")
                        print("   This indicates the followup intent is not working correctly.")
                    else:
                        print("   ? Unclear response type")
                        
                else:
                    print(f"   Error in second request: {response2.status_code}")
                    print(f"   Response: {response2.text}")
                    
            else:
                print("   ✗ Followup intent not triggered")
                print("   Expected response asking for service name")
                
        else:
            print(f"   Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("   ✗ Connection error - make sure your backend is running on the correct port")
    except Exception as e:
        print(f"   ✗ Error: {e}")

def test_direct_query():
    """Test direct query to compare"""
    
    print("\n=== Testing Direct Query for Comparison ===")
    
    print("\n3. Sending direct 'fees for mutation of land' query...")
    
    request_data = {
        "session_id": "test_session_direct_456",
        "user_input": "fees for mutation of land",
        "step": 1,
        "response_type": "text",
        "collection_name": COLLECTION_NAME
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/chat",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   Response: {json.dumps(result, indent=2)}")
            
            response_text = result.get("response", "").lower()
            if "fees" in response_text or "fee" in response_text or "cost" in response_text or "charge" in response_text:
                print("   ✓ Direct query returned fees information")
            else:
                print("   ? Direct query response unclear")
                
        else:
            print(f"   Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ✗ Error: {e}")

if __name__ == "__main__":
    print("Make sure your backend is running before running this test!")
    print("Press Enter to continue or Ctrl+C to cancel...")
    input()
    
    test_followup_flow()
    test_direct_query()
    
    print("\n=== Test Complete ===")
    print("\nIf the followup flow shows timeline instead of fees,")
    print("then the session data is not being maintained correctly.")
