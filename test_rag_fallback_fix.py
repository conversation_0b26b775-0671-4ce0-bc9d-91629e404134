#!/usr/bin/env python3
"""
Test script to verify that RAG fallback is working correctly in Step 1 direct general queries.
This test will send a query that should trigger fallback and verify RAG API is called.
"""

import requests
import json
import time

def test_rag_fallback():
    """Test that RAG API is called when <PERSON><PERSON><PERSON><PERSON> returns fallback"""
    
    # API endpoint
    url = "http://localhost:8020/chatbot/step"
    
    # Test payload - using a query that should trigger fallback
    payload = {
        "session_id": "test_session_123",
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "user_input": "what is kanyashree?",  # This query from the log that triggered fallback
        "step": 1,
        "response_type": "text"
    }
    
    print("Testing RAG fallback functionality...")
    print(f"Sending query: '{payload['user_input']}'")
    print("Expected behavior: Milvu<PERSON> should return fallback, then RAG API should be called")
    print("-" * 60)
    
    try:
        # Send the request
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"Response status code: {response.status_code}")
            print(f"Intent name: {result.get('intent_name', 'N/A')}")
            print(f"Response length: {len(result.get('response', ''))}")
            print(f"Response preview: {result.get('response', '')[:200]}...")
            
            # Check if RAG was used (look for disclaimer or longer response)
            response_text = result.get('response', '')
            if 'Disclaimer: This is an AI generated Response' in response_text:
                print("✅ RAG API was successfully called! (Disclaimer found)")
                return True
            elif len(response_text) > 100 and 'Thank you for your query!' not in response_text:
                print("✅ RAG API likely called (longer response without fallback message)")
                return True
            elif 'Thank you for your query! Could you please clarify' in response_text:
                print("❌ Still getting fallback message - RAG API not called")
                return False
            else:
                print("⚠️  Unclear if RAG was called - response doesn't match expected patterns")
                print(f"Full response: {response_text}")
                return False
                
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_multiple_queries():
    """Test multiple queries to ensure RAG fallback works consistently"""
    
    test_queries = [
        "what is kanyashree?",
        "tell me about digital india",
        "what are the benefits of startup india?",
        "explain about skill development programs"
    ]
    
    print("\n" + "="*60)
    print("Testing multiple queries for RAG fallback consistency")
    print("="*60)
    
    results = []
    for i, query in enumerate(test_queries, 1):
        print(f"\nTest {i}/4: '{query}'")
        print("-" * 40)
        
        payload = {
            "session_id": f"test_session_{i}",
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "user_input": query,
            "step": 1,
            "response_type": "text"
        }
        
        try:
            response = requests.post("http://localhost:8020/chatbot/step", json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                if 'Disclaimer: This is an AI generated Response' in response_text:
                    print("✅ RAG API called successfully")
                    results.append(True)
                elif 'Thank you for your query! Could you please clarify' in response_text:
                    print("❌ Still getting fallback message")
                    results.append(False)
                else:
                    print("⚠️  Unclear result")
                    results.append(None)
            else:
                print(f"❌ Request failed: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
        
        # Small delay between requests
        time.sleep(1)
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    successful = sum(1 for r in results if r is True)
    failed = sum(1 for r in results if r is False)
    unclear = sum(1 for r in results if r is None)
    
    print(f"✅ Successful RAG calls: {successful}/{len(test_queries)}")
    print(f"❌ Failed (still fallback): {failed}/{len(test_queries)}")
    print(f"⚠️  Unclear results: {unclear}/{len(test_queries)}")
    
    if successful == len(test_queries):
        print("\n🎉 All tests passed! RAG fallback is working correctly.")
    elif successful > 0:
        print(f"\n⚠️  Partial success. {successful} out of {len(test_queries)} queries used RAG.")
    else:
        print("\n❌ All tests failed. RAG fallback is not working.")

if __name__ == "__main__":
    print("RAG Fallback Fix Test")
    print("=" * 60)
    
    # Wait a moment for server to be ready
    print("Waiting 2 seconds for server to be ready...")
    time.sleep(2)
    
    # Run single test first
    success = test_rag_fallback()
    
    if success:
        # If first test passes, run multiple tests
        test_multiple_queries()
    else:
        print("\nFirst test failed. Skipping multiple query tests.")
        print("Please check the server logs for more details.")
