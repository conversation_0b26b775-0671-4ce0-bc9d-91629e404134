#!/usr/bin/env python3
"""
Test script to verify the unified RAG fallback functions work correctly.
This tests the functions directly without needing the full server.
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_unified_functions():
    """Test the unified RAG fallback functions"""
    
    print("Testing Unified RAG Fallback Functions")
    print("=" * 50)
    
    try:
        # Import the main module to get access to the functions
        import main
        
        print("✅ Successfully imported main module")
        
        # Test 1: should_trigger_rag_fallback function
        print("\n1. Testing should_trigger_rag_fallback function:")
        print("-" * 40)
        
        # Test case 1: Fallback intent
        result1 = {"intent_name": "fallback", "response": "Some response"}
        should_trigger1 = main.should_trigger_rag_fallback(result1)
        print(f"Test 1a - Fallback intent: {should_trigger1} (Expected: True)")
        
        # Test case 2: Empty response
        result2 = {"intent_name": "some_intent", "response": ""}
        should_trigger2 = main.should_trigger_rag_fallback(result2)
        print(f"Test 1b - Empty response: {should_trigger2} (Expected: True)")
        
        # Test case 3: Fallback message
        result3 = {"intent_name": "some_intent", "response": "Thank you for your query! Could you please clarify or provide more details so that I can assist you effectively?"}
        should_trigger3 = main.should_trigger_rag_fallback(result3)
        print(f"Test 1c - Fallback message: {should_trigger3} (Expected: True)")
        
        # Test case 4: Normal response (should not trigger)
        result4 = {"intent_name": "normal_intent", "response": "This is a normal response"}
        should_trigger4 = main.should_trigger_rag_fallback(result4)
        print(f"Test 1d - Normal response: {should_trigger4} (Expected: False)")
        
        # Test 2: unified_rag_fallback function (mock test)
        print("\n2. Testing unified_rag_fallback function structure:")
        print("-" * 40)
        
        # Check if the function exists and is callable
        if hasattr(main, 'unified_rag_fallback') and callable(main.unified_rag_fallback):
            print("✅ unified_rag_fallback function exists and is callable")
            
            # We won't actually call it since it requires RAG API, but we can check its signature
            import inspect
            sig = inspect.signature(main.unified_rag_fallback)
            print(f"✅ Function signature: {sig}")
            
        else:
            print("❌ unified_rag_fallback function not found or not callable")
        
        # Test 3: Check if functions are properly integrated
        print("\n3. Integration Check:")
        print("-" * 40)
        
        # Check if the functions are available in the main module
        functions_to_check = [
            'should_trigger_rag_fallback',
            'unified_rag_fallback',
            'json_load_rag',
            'rag_api'
        ]
        
        for func_name in functions_to_check:
            if hasattr(main, func_name):
                print(f"✅ {func_name} - Available")
            else:
                print(f"❌ {func_name} - Missing")
        
        print("\n4. Summary:")
        print("-" * 40)
        
        # Count successful tests
        successful_tests = [should_trigger1, should_trigger2, should_trigger3, not should_trigger4]
        success_count = sum(successful_tests)
        
        if success_count == 4:
            print("🎉 All function tests passed!")
            print("✅ should_trigger_rag_fallback works correctly")
            print("✅ unified_rag_fallback function is available")
            print("✅ Functions are properly integrated")
            return True
        else:
            print(f"⚠️  {success_count}/4 tests passed")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_fallback_logic_locations():
    """Test that the fallback logic has been updated in the right places"""
    
    print("\n" + "=" * 50)
    print("Testing Fallback Logic Integration")
    print("=" * 50)
    
    try:
        # Read the main.py file to check for the updated patterns
        with open('main.py', 'r') as f:
            content = f.read()
        
        # Check for unified function usage patterns
        patterns_to_find = [
            'should_trigger_rag_fallback(result)',
            'unified_rag_fallback(',
            'def should_trigger_rag_fallback(',
            'def unified_rag_fallback('
        ]
        
        print("Checking for updated patterns in main.py:")
        print("-" * 40)
        
        for pattern in patterns_to_find:
            count = content.count(pattern)
            if count > 0:
                print(f"✅ '{pattern}' found {count} time(s)")
            else:
                print(f"❌ '{pattern}' not found")
        
        # Check for old patterns that should be replaced
        old_patterns = [
            'rag_res = rag_api(link_data=link_data["silpasathi_links"]["link"], question=user_input)',
            'rag_result = rag_api(link_data=link_data["silpasathi_links"]["link"], question=query_with_context)'
        ]
        
        print("\nChecking for old patterns (should be minimal):")
        print("-" * 40)
        
        for pattern in old_patterns:
            count = content.count(pattern)
            if count == 0:
                print(f"✅ Old pattern removed: '{pattern[:50]}...'")
            else:
                print(f"⚠️  Old pattern still exists {count} time(s): '{pattern[:50]}...'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking file: {e}")
        return False

if __name__ == "__main__":
    print("Unified RAG Functions Test Suite")
    print("=" * 60)
    
    # Test 1: Function functionality
    success1 = test_unified_functions()
    
    # Test 2: Integration check
    success2 = test_fallback_logic_locations()
    
    print("\n" + "=" * 60)
    print("FINAL RESULTS")
    print("=" * 60)
    
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Unified RAG functions are working correctly")
        print("✅ Integration appears successful")
        print("\nNext step: Test with actual server and RAG API calls")
    else:
        print("❌ Some tests failed")
        if not success1:
            print("❌ Function tests failed")
        if not success2:
            print("❌ Integration tests failed")
        print("\nPlease review the issues above")
