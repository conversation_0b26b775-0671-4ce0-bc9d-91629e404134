#!/usr/bin/env python3
"""
Debug script to test the session data storage and retrieval for the followup intent issue.
This script simulates the exact flow that's causing the problem.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import store_session_data, get_session_data, session_data_store

def test_followup_session_flow():
    """Test the exact flow that's causing the issue"""
    
    print("=== Testing Followup Session Flow ===")
    
    # Simulate session ID
    test_session_id = "test_session_123"
    
    print(f"1. Initial session state:")
    print(f"   Session data store: {session_data_store}")
    
    # Step 1: User types "fees" - system should store followup intent
    print(f"\n2. Simulating user typing 'fees':")
    store_session_data(test_session_id, {
        'intent_related_text': 'fees',
        'followup_intent': 'ask_fees',
        'awaiting_service_name': True
    })
    
    print(f"   Stored session data: {session_data_store.get(test_session_id, {})}")
    
    # Step 2: Check if data can be retrieved
    print(f"\n3. Retrieving stored session data:")
    intent_related_text = get_session_data(test_session_id, "intent_related_text")
    followup_intent = get_session_data(test_session_id, "followup_intent")
    awaiting_service_name = get_session_data(test_session_id, "awaiting_service_name")
    
    print(f"   intent_related_text: '{intent_related_text}'")
    print(f"   followup_intent: '{followup_intent}'")
    print(f"   awaiting_service_name: '{awaiting_service_name}'")
    
    # Step 3: Check the condition that should trigger concatenation
    print(f"\n4. Checking concatenation condition:")
    condition_met = intent_related_text and followup_intent and awaiting_service_name
    print(f"   Condition (intent_related_text and followup_intent and awaiting_service_name): {condition_met}")
    
    if condition_met:
        user_input = "mutation of land"
        concatenated_query = f"{intent_related_text} for {user_input}"
        print(f"   Would concatenate to: '{concatenated_query}'")
        
        # Step 4: Clear session data (as done in the actual code)
        print(f"\n5. Clearing session data:")
        store_session_data(test_session_id, {
            'intent_related_text': None,
            'followup_intent': None,
            'awaiting_service_name': False
        })
        print(f"   Session data after clearing: {session_data_store.get(test_session_id, {})}")
    else:
        print(f"   ERROR: Concatenation condition not met!")
        print(f"   This means the followup flow will not work correctly.")
    
    print(f"\n=== Test Complete ===")

def test_session_data_persistence():
    """Test if session data persists across multiple calls"""
    
    print("\n=== Testing Session Data Persistence ===")
    
    test_session_id = "persistence_test_456"
    
    # Store data
    print("1. Storing test data...")
    store_session_data(test_session_id, {
        'test_key': 'test_value',
        'another_key': 'another_value'
    })
    
    # Retrieve immediately
    print("2. Retrieving immediately...")
    value1 = get_session_data(test_session_id, 'test_key')
    value2 = get_session_data(test_session_id, 'another_key')
    print(f"   test_key: {value1}")
    print(f"   another_key: {value2}")
    
    # Update one key
    print("3. Updating one key...")
    store_session_data(test_session_id, {
        'test_key': 'updated_value'
    })
    
    # Check if both keys still exist
    print("4. Checking after update...")
    value1 = get_session_data(test_session_id, 'test_key')
    value2 = get_session_data(test_session_id, 'another_key')
    print(f"   test_key: {value1}")
    print(f"   another_key: {value2}")
    
    print("=== Persistence Test Complete ===")

def test_session_retrieval_issue():
    """Test the specific issue where session data is not being retrieved"""

    print("\n=== Testing Session Retrieval Issue ===")

    session_id = "72dcffa9-e15c-48bc-a81d-303c0af2d541"  # Same as in your logs

    print("1. Simulating first request (fees)...")
    # Store session data as it would be stored after "fees" query
    store_session_data(session_id, {
        'intent_related_text': 'fees',
        'followup_intent': 'ask_fees',
        'awaiting_service_name': True
    })

    print(f"   Session data stored: {session_data_store.get(session_id, {})}")

    print("2. Simulating second request (mutation of land)...")
    # Check if session data can be retrieved (as it should be in the second request)
    intent_related_text = get_session_data(session_id, "intent_related_text")
    followup_intent = get_session_data(session_id, "followup_intent")
    awaiting_service_name = get_session_data(session_id, "awaiting_service_name")

    print(f"   Retrieved intent_related_text: '{intent_related_text}'")
    print(f"   Retrieved followup_intent: '{followup_intent}'")
    print(f"   Retrieved awaiting_service_name: '{awaiting_service_name}'")

    # Test the condition
    condition = awaiting_service_name and intent_related_text and followup_intent
    print(f"   Followup condition met: {condition}")

    if condition:
        user_input = "mutation of land"
        concatenated_query = f"{intent_related_text} for {user_input}"
        print(f"   ✓ Would concatenate to: '{concatenated_query}'")
        print("   ✓ This should search for fees information, not timeline!")
    else:
        print("   ✗ ERROR: Followup condition not met!")
        print("   This explains why you're getting timeline instead of fees.")

if __name__ == "__main__":
    test_followup_session_flow()
    test_session_data_persistence()
    test_session_retrieval_issue()

    print(f"\nFinal session_data_store state:")
    for session_id, data in session_data_store.items():
        print(f"  {session_id}: {data}")
