#!/usr/bin/env python3
"""
Test script to simulate the service selection flow and verify the service query interface.
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8020"
COLLECTION_NAME = "collection_b5e7c017_1a06_4857_819c_6a038133dd94"

def test_service_selection_flow():
    """Test the complete service selection flow"""
    
    print("=== Testing Service Selection Flow ===")
    
    session_id = "test_service_selection_123"
    
    # Step 1: Select main option "Apply for licence/clearance"
    print("\n1. Selecting 'Apply for licence/clearance'...")
    
    request_data = {
        "session_id": session_id,
        "step": 1,
        "response_type": "options",
        "collection_name": COLLECTION_NAME,
        "user_response": {
            "caption": "main_option",
            "value": "1. Apply for licence/clearance"
        }
    }
    
    response = send_request(request_data)
    if response:
        print(f"   Step 1 Response: {response.get('response', 'No response')}")
        print(f"   Next Step: {response.get('step', 'Unknown')}")
    
    # Step 2: Select service type "Pre-establishment"
    print("\n2. Selecting 'Pre-establishment'...")
    
    request_data = {
        "session_id": session_id,
        "step": 2,
        "response_type": "options",
        "collection_name": COLLECTION_NAME,
        "user_response": {
            "caption": "service_type",
            "value": "1. Pre-establishment"
        }
    }
    
    response = send_request(request_data)
    if response:
        print(f"   Step 2 Response: {response.get('response', 'No response')}")
        print(f"   Next Step: {response.get('step', 'Unknown')}")
    
    # Step 3: Select a specific service
    print("\n3. Selecting 'Drug License (Wholesale)'...")

    request_data = {
        "session_id": session_id,
        "step": 4,  # Changed to step 4 to match what frontend sends
        "response_type": "options",
        "collection_name": COLLECTION_NAME,
        "user_response": {
            "caption": "selected_service",
            "value": "14. Drug License (Wholesale)"
        }
    }
    
    response = send_request(request_data)
    if response:
        print(f"   Step 3 Response: {response.get('response', 'No response')}")
        print(f"   Next Step: {response.get('step', 'Unknown')}")
        print(f"   Response Type: {response.get('response_type', 'Unknown')}")
        
        # Check if we got the service query interface
        if "Selected Service:" in response.get('response', ''):
            print("   ✓ Service query interface displayed correctly!")
            
            # Check if we have service query buttons
            option_list = response.get('option_list', [])
            if isinstance(option_list, list) and len(option_list) > 0:
                print(f"   ✓ Found {len(option_list)} service query buttons")
                for i, button in enumerate(option_list):
                    print(f"     Button {i+1}: {button}")
            else:
                print("   ✗ No service query buttons found")
        else:
            print("   ✗ Service query interface not displayed")
    
    # Step 4: Test button click - "Required documents"
    print("\n4. Testing 'Required documents' button click...")
    
    request_data = {
        "session_id": session_id,
        "step": 4,
        "response_type": "text",
        "collection_name": COLLECTION_NAME,
        "user_response": {
            "caption": "service_query",
            "value": "Required documents for 14. Drug License (Wholesale)"
        }
    }
    
    response = send_request(request_data)
    if response:
        print(f"   Button Click Response: {response.get('response', 'No response')[:200]}...")
        if "Drug License" in response.get('response', ''):
            print("   ✓ Service-specific response received!")
        else:
            print("   ✗ Generic or incorrect response received")
    
    # Step 5: Test text input - "timeline"
    print("\n5. Testing text input 'timeline'...")
    
    request_data = {
        "session_id": session_id,
        "step": 4,
        "response_type": "text",
        "collection_name": COLLECTION_NAME,
        "user_input": "timeline"
    }
    
    response = send_request(request_data)
    if response:
        print(f"   Text Input Response: {response.get('response', 'No response')[:200]}...")
        if "Drug License" in response.get('response', ''):
            print("   ✓ Service-specific response received!")
        else:
            print("   ✗ Generic or incorrect response received")

def send_request(request_data):
    """Send request to the backend and return response"""
    try:
        response = requests.post(
            f"{API_BASE_URL}/chatbot/step",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"   Error: HTTP {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("   ✗ Connection error - make sure your backend is running")
        return None
    except Exception as e:
        print(f"   ✗ Error: {e}")
        return None

if __name__ == "__main__":
    print("Make sure your backend is running on localhost:8020")
    print("Press Enter to continue or Ctrl+C to cancel...")
    input()
    
    test_service_selection_flow()
    
    print("\n=== Test Complete ===")
    print("\nIf the service query interface is not showing,")
    print("check the backend logs for any errors in service selection processing.")
