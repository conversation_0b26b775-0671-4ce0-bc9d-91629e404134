#!/usr/bin/env python3
"""
Test the new simpler approach for service-specific fallback.
This approach includes the service name in the query to let <PERSON><PERSON><PERSON><PERSON> scoring handle relevance.
"""

import requests
import json
import time

def test_simple_service_approach():
    """Test the simpler approach with service name included in query"""
    
    print("🧪 Testing Simple Service Approach")
    print("=" * 70)
    print("New approach: Include service name in query → Let Milvus scoring handle relevance")
    print("Expected behavior:")
    print("- Service-related queries → High score → Return response")
    print("- Non-service queries → Low score → Trigger fallback")
    print("=" * 70)
    
    url = "http://localhost:8020/chatbot/step"
    session_id = "test_simple_approach"
    
    try:
        # Setup: Create session with 'Mutation of Land' service
        print("\n📋 Setting up session with 'Mutation of Land' service")
        print("-" * 50)
        
        # Step 1: Set main option
        setup1_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "step": 1,
            "user_response": {
                "caption": "main_option",
                "value": "1. Apply for licence/clearance"
            }
        }
        
        response1 = requests.post(url, json=setup1_payload, timeout=30)
        print(f"Main option setup: {response1.status_code}")
        
        time.sleep(1)
        
        # Step 2: Set service type
        setup2_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "step": 2,
            "user_response": {
                "caption": "service_type",
                "value": "1. Pre-establishment"
            }
        }
        
        response2 = requests.post(url, json=setup2_payload, timeout=30)
        print(f"Service type setup: {response2.status_code}")
        
        time.sleep(1)
        
        # Step 3: Set selected service
        setup3_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "step": 3,
            "user_response": {
                "caption": "selected_service",
                "value": "11. Mutation of Land"
            }
        }
        
        response3 = requests.post(url, json=setup3_payload, timeout=30)
        print(f"Selected service setup: {response3.status_code}")
        print("✅ Session setup complete")
        
        time.sleep(2)
        
        # Test cases
        test_cases = [
            {
                "name": "Service-related query (fees)",
                "query": "what is the fees?",
                "expected_behavior": "Should get high score and return Milvus response",
                "should_trigger_fallback": False
            },
            {
                "name": "General query (kanyashree)",
                "query": "what is kanyashree?",
                "expected_behavior": "Should get low score and trigger service fallback",
                "should_trigger_fallback": True
            },
            {
                "name": "Different service query (trade licence)",
                "query": "what is the trade licence apply process?",
                "expected_behavior": "Should get low score and trigger service fallback",
                "should_trigger_fallback": True
            },
            {
                "name": "General query (silpasathi)",
                "query": "what is silpasathi?",
                "expected_behavior": "Should get low score and trigger service fallback",
                "should_trigger_fallback": True
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test {i}: {test_case['name']}")
            print("-" * 50)
            print(f"Query: '{test_case['query']}'")
            print(f"Expected: {test_case['expected_behavior']}")
            
            test_payload = {
                "session_id": session_id,
                "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
                "user_input": test_case["query"],
                "step": 4,
                "response_type": "text"
            }
            
            response = requests.post(url, json=test_payload, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                intent_name = result.get('intent_name', '')
                
                print(f"✅ Request successful: {response.status_code}")
                print(f"Intent: {intent_name}")
                print(f"Response preview: {response_text[:100]}...")
                
                # Check if fallback was triggered
                fallback_triggered = "This query is not related to" in response_text
                
                if fallback_triggered == test_case["should_trigger_fallback"]:
                    print(f"✅ PASS: Behavior matches expectation")
                    results.append(True)
                else:
                    if test_case["should_trigger_fallback"]:
                        print(f"❌ FAIL: Expected fallback but got Milvus response")
                    else:
                        print(f"❌ FAIL: Expected Milvus response but got fallback")
                    results.append(False)
            else:
                print(f"❌ Request failed: {response.status_code}")
                results.append(False)
            
            time.sleep(2)
        
        # Final assessment
        print("\n" + "=" * 70)
        print("🎯 SIMPLE APPROACH TEST RESULTS")
        print("=" * 70)
        
        passed = sum(results)
        total = len(results)
        
        for i, (test_case, result) in enumerate(zip(test_cases, results), 1):
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"Test {i} ({test_case['name']}): {status}")
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("\n🎉 SIMPLE APPROACH: ✅ WORKING PERFECTLY!")
            print("✅ Service name in query approach is successful")
            print("✅ Milvus scoring automatically handles relevance")
            print("✅ No complex custom logic needed")
            print("✅ User's issue resolved with elegant solution")
            
            print("\n📋 HOW IT WORKS:")
            print("1. Query: 'what is the fees?' → Search: 'what is the fees? 11. Mutation of Land'")
            print("   → High score → Returns response")
            print("2. Query: 'what is kanyashree?' → Search: 'what is kanyashree? 11. Mutation of Land'")
            print("   → Low score → Triggers fallback")
            
        else:
            print("\n⚠️  SIMPLE APPROACH: NEEDS REFINEMENT")
            print("The service name inclusion approach may need adjustment")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Run the simple service approach test"""
    
    print("Simple Service Approach Test")
    print("=" * 70)
    print("Testing the user's suggested approach:")
    print("Include service name in query → Let Milvus scoring handle relevance")
    print("This replaces the complex is_response_service_related function")
    print("=" * 70)
    
    # Wait for server to be ready
    print("⏳ Waiting 3 seconds for server to be ready...")
    time.sleep(3)
    
    success = test_simple_service_approach()
    
    if success:
        print("\n🎉 SUCCESS: The simple approach works perfectly!")
        print("Your suggestion was brilliant - much cleaner and more reliable!")
    else:
        print("\n⚠️  The simple approach may need fine-tuning.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
