-- =====================================================
-- DATABASE CLEANUP FOR EODB CHATBOT - FINAL VERSION
-- =====================================================
-- This script removes legacy sector/investment data and aligns database with current simplified flow
-- Execute this script to clean up your database and make it production-ready

-- =====================================================
-- STEP 1: BACKUP CURRENT DATA (OPTIONAL)
-- =====================================================
-- Uncomment these lines if you want to backup before cleanup
-- CREATE TABLE backup_eodb_step_flow_final AS SELECT * FROM eodb_step_flow_final;
-- CREATE TABLE backup_eodb_master_data_final AS SELECT * FROM eodb_master_data_final;
-- CREATE TABLE backup_eodb_master_data_types_final AS SELECT * FROM eodb_master_data_types_final;

-- =====================================================
-- STEP 2: REMOVE LEGACY STEP FLOW CONFIGURATIONS
-- =====================================================

-- Remove sector/investment step flows that conflict with current simplified flow
DELETE FROM eodb_step_flow_final 
WHERE dependent_on_value IN ('2. Know application status', '3. Raise any query / Grievance', '4. FAQ')
AND input_caption = 'Choose sector or industry:';

-- Remove investment step flow
DELETE FROM eodb_step_flow_final 
WHERE input_caption = 'What is the total investment value for all the units in your organisation?';

-- =====================================================
-- STEP 3: REMOVE UNUSED MASTER DATA
-- =====================================================

-- Remove all sectors data
DELETE FROM eodb_master_data_final 
WHERE type_id = (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'sectors');

-- Remove all investments data  
DELETE FROM eodb_master_data_final 
WHERE type_id = (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'investments');

-- =====================================================
-- STEP 4: REMOVE UNUSED MASTER DATA TYPES
-- =====================================================

-- Remove sectors type
DELETE FROM eodb_master_data_types_final WHERE type_name = 'sectors';

-- Remove investments type
DELETE FROM eodb_master_data_types_final WHERE type_name = 'investments';

-- =====================================================
-- STEP 5: CLEAN UP STEP FLOW FOR CURRENT SIMPLIFIED FLOW
-- =====================================================

-- Ensure clean step flow configuration
DELETE FROM eodb_step_flow_final;

-- Insert correct step flow for current simplified flow
INSERT INTO eodb_step_flow_final (step_number, type_id, next_step, response_type, input_caption, dependent_on_step, dependent_on_value) VALUES
-- Step 1: Main options (always shown)
(1, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'main_options'), 2, 'options', 'I can help you with the following services:', NULL, NULL),

-- Step 2: Service types (only for licence/clearance)
(2, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'service_types'), 3, 'options', 'Select application type:', 1, '1. Apply for licence/clearance'),

-- Step 3: Pre-establishment services
(3, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'pre_establishment'), 4, 'options', 'Select the Pre-establishment service:', 2, '1. Pre-establishment'),

-- Step 3: Pre-operational services  
(3, (SELECT id FROM eodb_master_data_types_final WHERE type_name = 'pre_operation'), 4, 'options', 'Select the Pre-operation service:', 2, '2. Pre-operational')

ON CONFLICT (step_number, dependent_on_step, dependent_on_value) DO NOTHING;

-- =====================================================
-- STEP 6: ADD MISSING CONFIGURATION FOR GENERAL QUERIES
-- =====================================================

-- Ensure general_query type exists for step 6
INSERT INTO eodb_master_data_types_final (type_name, display_name, description) VALUES
('general_query', 'Please enter your query:', 'General query input')
ON CONFLICT (type_name) DO NOTHING;

-- =====================================================
-- STEP 7: VERIFY CLEANUP RESULTS
-- =====================================================

-- Show remaining master data types (should only show needed ones)
SELECT 'REMAINING MASTER DATA TYPES:' as info;
SELECT type_name, display_name, description FROM eodb_master_data_types_final ORDER BY type_name;

-- Show step flow configuration (should be clean and simple)
SELECT 'STEP FLOW CONFIGURATION:' as info;
SELECT step_number, response_type, input_caption, next_step, dependent_on_step, dependent_on_value 
FROM eodb_step_flow_final ORDER BY step_number, dependent_on_step, dependent_on_value;

-- Count records in each master data type
SELECT 'MASTER DATA COUNTS:' as info;
SELECT 
    mdt.type_name,
    COUNT(md.id) as record_count
FROM eodb_master_data_types_final mdt
LEFT JOIN eodb_master_data_final md ON mdt.id = md.type_id
GROUP BY mdt.type_name, mdt.id
ORDER BY mdt.type_name;

-- =====================================================
-- STEP 8: OPTIMIZATION
-- =====================================================

-- Vacuum and analyze tables for better performance
VACUUM ANALYZE eodb_master_data_final;
VACUUM ANALYZE eodb_master_data_types_final;
VACUUM ANALYZE eodb_step_flow_final;

-- =====================================================
-- STEP 9: FINAL VERIFICATION
-- =====================================================

SELECT 'DATABASE CLEANUP COMPLETED SUCCESSFULLY!' as status;
SELECT 'Your database is now aligned with the current simplified flow.' as message;
