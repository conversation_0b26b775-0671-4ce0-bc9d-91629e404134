#!/usr/bin/env python3
"""
Quick test to confirm RAG fallback is working
"""

import requests
import json

def quick_test():
    url = "http://localhost:8020/chatbot/step"
    
    payload = {
        "session_id": "quick_test_456",
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "user_input": "what is digital india?",
        "step": 1,
        "response_type": "text"
    }
    
    print("Quick test: Sending query that should trigger RAG fallback...")
    
    try:
        response = requests.post(url, json=payload, timeout=90)  # Longer timeout
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            
            print(f"✅ Success! Response received:")
            print(f"Intent: {result.get('intent_name', 'N/A')}")
            print(f"Response length: {len(response_text)} characters")
            
            if 'Disclaimer: This is an AI generated Response' in response_text:
                print("✅ RAG API was called successfully! (Disclaimer found)")
                print(f"Response preview: {response_text[:150]}...")
                return True
            else:
                print("❌ RAG API not called - no disclaimer found")
                print(f"Full response: {response_text}")
                return False
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("Quick RAG Fallback Test")
    print("=" * 40)
    success = quick_test()
    
    if success:
        print("\n🎉 RAG fallback fix is working correctly!")
    else:
        print("\n❌ RAG fallback fix needs more investigation.")
