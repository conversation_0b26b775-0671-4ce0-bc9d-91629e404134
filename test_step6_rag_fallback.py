#!/usr/bin/env python3
"""
Test script to verify that RAG fallback is working correctly in Step 6 post-status queries.
This simulates the scenario where user has completed status check and asks general questions.
"""

import requests
import json
import time

def create_session_with_status_completed():
    """Create a session and simulate status check completion"""
    
    # First, create session and set up status check completion
    session_url = "http://localhost:8020/session_manager"
    session_payload = {
        "session_type": "eodb_chatbot"
    }
    
    try:
        session_response = requests.post(session_url, json=session_payload, timeout=30)
        if session_response.status_code == 200:
            session_data = session_response.json()
            session_id = session_data.get("session_id")
            print(f"✅ Session created: {session_id}")
            
            # Simulate the status check flow to set up the session properly
            # Step 1: Select "Know application status"
            step1_payload = {
                "session_id": session_id,
                "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
                "user_input": "",
                "step": 1,
                "user_response": {
                    "caption": "2. Know application status",
                    "value": "2. Know application status"
                },
                "response_type": "options"
            }
            
            step1_response = requests.post("http://localhost:8020/chatbot/step", json=step1_payload, timeout=30)
            if step1_response.status_code == 200:
                print("✅ Step 1: Selected 'Know application status'")
                
                # Step 2: Enter CAF number
                step2_payload = {
                    "session_id": session_id,
                    "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
                    "user_input": "CAF2023009970",
                    "step": 2,
                    "response_type": "text"
                }
                
                step2_response = requests.post("http://localhost:8020/chatbot/step", json=step2_payload, timeout=30)
                if step2_response.status_code == 200:
                    print("✅ Step 2: Entered CAF number")
                    
                    # Step 3: Enter OTP
                    step3_payload = {
                        "session_id": session_id,
                        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
                        "user_input": "123456",
                        "step": 3,
                        "response_type": "text"
                    }
                    
                    step3_response = requests.post("http://localhost:8020/chatbot/step", json=step3_payload, timeout=30)
                    if step3_response.status_code == 200:
                        print("✅ Step 3: Entered OTP")
                        
                        # Step 4: Select service (this should complete status check)
                        step4_payload = {
                            "session_id": session_id,
                            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
                            "user_input": "",
                            "step": 4,
                            "user_response": {
                                "caption": "Consent to Operate under the Air (Prevention and Control of Pollution) Act, 1981)& Water Act (Prevention and Control of Pollution) Act, 1974",
                                "value": "Consent to Operate under the Air (Prevention and Control of Pollution) Act, 1981)& Water Act (Prevention and Control of Pollution) Act, 1974"
                            },
                            "response_type": "options"
                        }
                        
                        step4_response = requests.post("http://localhost:8020/chatbot/step", json=step4_payload, timeout=30)
                        if step4_response.status_code == 200:
                            print("✅ Step 4: Selected service - Status check should be completed")
                            return session_id
                        else:
                            print(f"❌ Step 4 failed: {step4_response.status_code}")
                    else:
                        print(f"❌ Step 3 failed: {step3_response.status_code}")
                else:
                    print(f"❌ Step 2 failed: {step2_response.status_code}")
            else:
                print(f"❌ Step 1 failed: {step1_response.status_code}")
        else:
            print(f"❌ Session creation failed: {session_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error setting up session: {e}")
    
    return None

def test_step6_rag_fallback(session_id):
    """Test Step 6 RAG fallback with a session that has completed status check"""
    
    # Test payload for Step 6 general query
    payload = {
        "session_id": session_id,
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "user_input": "what is kanyashree?",  # This should trigger fallback
        "step": 6,
        "response_type": "text"
    }
    
    print(f"\nTesting Step 6 RAG fallback...")
    print(f"Session ID: {session_id}")
    print(f"Query: '{payload['user_input']}'")
    print("Expected: Milvus fallback → RAG API call → Response with disclaimer")
    print("-" * 60)
    
    try:
        response = requests.post("http://localhost:8020/chatbot/step", json=payload, timeout=90)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            
            print("✅ Request successful!")
            print(f"Intent name: {result.get('intent_name', 'N/A')}")
            print(f"Response length: {len(response_text)} characters")
            
            # Check if RAG was used (look for disclaimer)
            if 'Disclaimer: This is an AI generated Response' in response_text:
                print("✅ RAG API was successfully called! (Disclaimer found)")
                print(f"Response preview: {response_text[:200]}...")
                return True
            elif 'Thank you for your query! Could you please clarify' in response_text:
                print("❌ Still getting fallback message - RAG API not called")
                print(f"Full response: {response_text}")
                return False
            elif len(response_text) > 100 and 'As on 04 March 2023' in response_text:
                print("✅ RAG API likely called (formatted response with date)")
                print(f"Response preview: {response_text[:200]}...")
                return True
            else:
                print("⚠️  Unclear if RAG was called")
                print(f"Full response: {response_text}")
                return False
                
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

def test_multiple_step6_queries(session_id):
    """Test multiple Step 6 queries"""
    
    test_queries = [
        "what is kanyashree?",
        "tell me about digital india",
        "what are startup benefits?",
        "explain skill development"
    ]
    
    print(f"\n{'='*60}")
    print("Testing Multiple Step 6 Queries")
    print(f"{'='*60}")
    
    results = []
    for i, query in enumerate(test_queries, 1):
        print(f"\nTest {i}/4: '{query}'")
        print("-" * 40)
        
        payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "user_input": query,
            "step": 6,
            "response_type": "text"
        }
        
        try:
            response = requests.post("http://localhost:8020/chatbot/step", json=payload, timeout=90)
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                if 'Disclaimer: This is an AI generated Response' in response_text:
                    print("✅ RAG API called successfully")
                    results.append(True)
                elif 'Thank you for your query! Could you please clarify' in response_text:
                    print("❌ Still getting fallback message")
                    results.append(False)
                else:
                    print("⚠️  Unclear result")
                    results.append(None)
            else:
                print(f"❌ Request failed: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
        
        time.sleep(2)  # Longer delay for Step 6 tests
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    successful = sum(1 for r in results if r is True)
    failed = sum(1 for r in results if r is False)
    unclear = sum(1 for r in results if r is None)
    
    print(f"✅ Successful RAG calls: {successful}/{len(test_queries)}")
    print(f"❌ Failed (still fallback): {failed}/{len(test_queries)}")
    print(f"⚠️  Unclear results: {unclear}/{len(test_queries)}")
    
    return successful == len(test_queries)

if __name__ == "__main__":
    print("Step 6 RAG Fallback Test")
    print("=" * 60)
    
    print("Setting up session with completed status check...")
    session_id = create_session_with_status_completed()
    
    if session_id:
        print(f"✅ Session setup complete: {session_id}")
        
        # Test single query
        success = test_step6_rag_fallback(session_id)
        
        if success:
            # Test multiple queries
            all_success = test_multiple_step6_queries(session_id)
            if all_success:
                print("\n🎉 All Step 6 RAG fallback tests passed!")
            else:
                print("\n⚠️  Some Step 6 tests had issues.")
        else:
            print("\nFirst test failed. Skipping multiple query tests.")
    else:
        print("❌ Failed to set up session. Cannot run Step 6 tests.")
