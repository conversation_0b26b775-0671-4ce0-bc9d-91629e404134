#!/usr/bin/env python3
"""
Comprehensive test to verify the complete RAG fallback solution works for both:
1. Step 1 direct general queries
2. Step 6 post-status queries
"""

import requests
import json
import time

def test_step1_rag_fallback():
    """Test Step 1 direct general query RAG fallback"""
    
    print("🧪 Testing Step 1 Direct General Query RAG Fallback")
    print("-" * 60)
    
    url = "http://localhost:8020/chatbot/step"
    
    payload = {
        "session_id": "test_step1_unified",
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "user_input": "what is kanyashree?",
        "step": 1,
        "response_type": "text"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            intent_name = result.get('intent_name', '')
            
            print(f"✅ Request successful!")
            print(f"Status: {response.status_code}")
            print(f"Intent: {intent_name}")
            print(f"Response length: {len(response_text)} chars")
            
            if 'Disclaimer: This is an AI generated Response' in response_text:
                print("✅ Step 1 RAG fallback working! (Disclaimer found)")
                print(f"Preview: {response_text[:100]}...")
                return True
            elif 'Thank you for your query!' in response_text:
                print("❌ Step 1 still showing fallback message")
                return False
            else:
                print("⚠️  Step 1 unclear result")
                print(f"Response: {response_text[:200]}...")
                return False
                
        else:
            print(f"❌ Step 1 request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Step 1 error: {e}")
        return False

def test_step6_rag_fallback():
    """Test Step 6 post-status query RAG fallback"""
    
    print("\n🧪 Testing Step 6 Post-Status Query RAG Fallback")
    print("-" * 60)
    
    url = "http://localhost:8020/chatbot/step"
    
    # Use the exact session ID from user's log
    session_id = "3046096d-aa99-46db-ac77-71bafcf71a0d"
    
    payload = {
        "session_id": session_id,
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "user_input": "what is kanyashree?",
        "step": 6,
        "response_type": "text"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            intent_name = result.get('intent_name', '')
            
            print(f"✅ Request successful!")
            print(f"Status: {response.status_code}")
            print(f"Intent: {intent_name}")
            print(f"Response length: {len(response_text)} chars")
            
            if 'Disclaimer: This is an AI generated Response' in response_text:
                print("✅ Step 6 RAG fallback working! (Disclaimer found)")
                print(f"Preview: {response_text[:100]}...")
                return True
            elif 'Thank you for your query!' in response_text:
                print("❌ Step 6 still showing fallback message")
                return False
            else:
                print("⚠️  Step 6 unclear result")
                print(f"Response: {response_text[:200]}...")
                return False
                
        else:
            print(f"❌ Step 6 request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Step 6 error: {e}")
        return False

def test_multiple_queries():
    """Test multiple different queries to ensure consistency"""
    
    print("\n🧪 Testing Multiple Queries for Consistency")
    print("-" * 60)
    
    test_queries = [
        "what is digital india?",
        "tell me about startup india",
        "explain skill development programs",
        "what are the benefits of make in india?"
    ]
    
    url = "http://localhost:8020/chatbot/step"
    results = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\nTest {i}/4: '{query}'")
        print("-" * 30)
        
        payload = {
            "session_id": f"test_multi_{i}",
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "user_input": query,
            "step": 1,
            "response_type": "text"
        }
        
        try:
            response = requests.post(url, json=payload, timeout=90)
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                if 'Disclaimer: This is an AI generated Response' in response_text:
                    print("✅ RAG working")
                    results.append(True)
                elif 'Thank you for your query!' in response_text:
                    print("❌ Fallback message")
                    results.append(False)
                else:
                    print("⚠️  Other response")
                    results.append(None)
            else:
                print(f"❌ Failed: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
        
        time.sleep(2)  # Small delay between requests
    
    return results

def main():
    """Run comprehensive RAG fallback tests"""
    
    print("🎯 COMPREHENSIVE RAG FALLBACK SOLUTION TEST")
    print("=" * 70)
    print("Testing the complete unified RAG fallback implementation")
    print("=" * 70)
    
    # Wait for server to be ready
    print("⏳ Waiting 3 seconds for server to be ready...")
    time.sleep(3)
    
    # Test 1: Step 1 RAG fallback
    step1_success = test_step1_rag_fallback()
    
    # Test 2: Step 6 RAG fallback (the main issue from user's log)
    step6_success = test_step6_rag_fallback()
    
    # Test 3: Multiple queries for consistency
    multi_results = test_multiple_queries()
    multi_success = sum(1 for r in multi_results if r is True)
    multi_total = len(multi_results)
    
    # Final summary
    print("\n" + "=" * 70)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 70)
    
    print(f"✅ Step 1 Direct General Query: {'PASS' if step1_success else 'FAIL'}")
    print(f"✅ Step 6 Post-Status Query: {'PASS' if step6_success else 'FAIL'}")
    print(f"✅ Multiple Query Consistency: {multi_success}/{multi_total} PASS")
    
    overall_success = step1_success and step6_success and (multi_success >= multi_total * 0.75)
    
    if overall_success:
        print("\n🎉 COMPREHENSIVE TEST: ✅ PASSED!")
        print("✅ Step 1 RAG fallback working correctly")
        print("✅ Step 6 RAG fallback working correctly (MAIN ISSUE FIXED)")
        print("✅ Unified RAG functions working consistently")
        print("✅ Code duplication eliminated")
        print("\n🎯 The user's original issue has been completely resolved!")
        
    else:
        print("\n❌ COMPREHENSIVE TEST: FAILED")
        if not step1_success:
            print("❌ Step 1 RAG fallback not working")
        if not step6_success:
            print("❌ Step 6 RAG fallback not working (MAIN ISSUE)")
        if multi_success < multi_total * 0.75:
            print(f"❌ Consistency issues ({multi_success}/{multi_total})")
    
    print("\n📋 IMPLEMENTATION SUMMARY:")
    print("-" * 40)
    print("✅ Created unified_rag_fallback() function")
    print("✅ Created should_trigger_rag_fallback() function")
    print("✅ Updated Step 1 handler to use unified functions")
    print("✅ Updated Step 6 handler to use unified functions")
    print("✅ Updated all 6 RAG fallback locations")
    print("✅ Eliminated code duplication")
    print("✅ Improved error handling and logging")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
