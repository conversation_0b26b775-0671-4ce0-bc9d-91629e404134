#!/usr/bin/env python3
"""
Test to prove that the is_response_service_related function works dynamically
for ANY service name, not just hardcoded for "Mutation of Land".
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dynamic_service_detection():
    """Test the function with different service names to prove it's dynamic"""
    
    print("🧪 Testing Dynamic Service Detection")
    print("=" * 60)
    print("Proving that is_response_service_related works for ANY service name")
    print("=" * 60)
    
    try:
        # Import the function from main module
        from main import is_response_service_related
        
        # Test cases with DIFFERENT service names
        test_cases = [
            {
                "service_name": "Trade License",
                "tests": [
                    {
                        "query": "what are the fees?",
                        "response": "Fee information will be provided during application",
                        "expected": True,
                        "reason": "Standard service query (fees) should be allowed"
                    },
                    {
                        "query": "what is kanyashree?",
                        "response": "Kanyashree is a scholarship scheme...",
                        "expected": False,
                        "reason": "General topic query should trigger fallback"
                    },
                    {
                        "query": "what is factory license process?",
                        "response": "Factory license requires environmental clearance...",
                        "expected": False,
                        "reason": "Different service query should trigger fallback"
                    }
                ]
            },
            {
                "service_name": "Factory License",
                "tests": [
                    {
                        "query": "what documents are required?",
                        "response": "Required documents for factory license include...",
                        "expected": True,
                        "reason": "Standard service query (documents) should be allowed"
                    },
                    {
                        "query": "what is silpasathi?",
                        "response": "Silpasathi is the Single Window Portal...",
                        "expected": False,
                        "reason": "General topic query should trigger fallback"
                    },
                    {
                        "query": "what is trade license process?",
                        "response": "Trade license application requires...",
                        "expected": False,
                        "reason": "Different service query should trigger fallback"
                    }
                ]
            },
            {
                "service_name": "Environmental Clearance",
                "tests": [
                    {
                        "query": "what is the timeline?",
                        "response": "Environmental clearance timeline is 60 days...",
                        "expected": True,
                        "reason": "Standard service query (timeline) should be allowed"
                    },
                    {
                        "query": "what is digital india?",
                        "response": "Digital India is a government initiative...",
                        "expected": False,
                        "reason": "General topic query should trigger fallback"
                    },
                    {
                        "query": "what is mutation of land process?",
                        "response": "Land mutation requires revenue documents...",
                        "expected": False,
                        "reason": "Different service query should trigger fallback"
                    }
                ]
            }
        ]
        
        total_tests = 0
        passed_tests = 0
        
        for service_case in test_cases:
            service_name = service_case["service_name"]
            print(f"\n📋 Testing Service: '{service_name}'")
            print("-" * 50)
            
            for test in service_case["tests"]:
                total_tests += 1
                
                result = is_response_service_related(
                    test["response"],
                    service_name,
                    test["query"]
                )
                
                status = "✅ PASS" if result == test["expected"] else "❌ FAIL"
                
                print(f"Query: '{test['query']}'")
                print(f"Expected: {test['expected']}, Got: {result}")
                print(f"Reason: {test['reason']}")
                print(f"Status: {status}")
                print()
                
                if result == test["expected"]:
                    passed_tests += 1
        
        print("=" * 60)
        print(f"🎯 DYNAMIC TEST RESULTS: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("\n🎉 PROOF: The function is COMPLETELY DYNAMIC!")
            print("✅ Works with ANY service name")
            print("✅ No hardcoding for specific services")
            print("✅ Uses generic detection patterns")
            print("✅ Dynamically extracts keywords from service names")
            return True
        else:
            print(f"\n⚠️  Some tests failed: {total_tests - passed_tests} failures")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Run the dynamic service detection test"""
    
    print("Dynamic Service Detection Test")
    print("=" * 70)
    print("This test proves that the service fallback function is NOT hardcoded")
    print("for any specific service name and works dynamically for ALL services.")
    print("=" * 70)
    
    success = test_dynamic_service_detection()
    
    if success:
        print("\n🎉 CONCLUSION: The function is completely dynamic and generic!")
        print("It will work correctly for ANY service in your database.")
    else:
        print("\n⚠️  The function may need further refinement for some edge cases.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
