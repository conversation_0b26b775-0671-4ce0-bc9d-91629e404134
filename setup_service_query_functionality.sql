-- Setup service query functionality for EoDB chatbot

-- Create service query buttons table if it doesn't exist
CREATE TABLE IF NOT EXISTS eodb_service_query_buttons_final (
    id SERIAL PRIMARY KEY,
    button_text VARCHAR(100) NOT NULL UNIQUE,
    query_template VARCHAR(200) NOT NULL,
    button_order INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Insert service query prompt message
INSERT INTO eodb_greeting_messages_final (message_key, message_text, is_active) VALUES
('service_query_prompt', 'Selected Service: {selected_service}

You can ask queries about this service or click the buttons below to get specific information:', TRUE)
ON CONFLICT (message_key) DO UPDATE SET
message_text = EXCLUDED.message_text,
updated_at = NOW();

-- Insert service fallback message
INSERT INTO eodb_greeting_messages_final (message_key, message_text, is_active) VALUES
('service_fallback_message', 'This query is not related to the selected service. If you want to ask about this service, type ''exit'' and ask in the general query section.', TRUE)
ON CONFLICT (message_key) DO UPDATE SET
message_text = EXCLUDED.message_text,
updated_at = NOW();

-- Insert wrong service followup message
INSERT INTO eodb_greeting_messages_final (message_key, message_text, is_active) VALUES
('wrong_service_followup_message', 'This service is not available in the knowledge base. Please enter a service name which is in the knowledge base for {intent_text}.', TRUE)
ON CONFLICT (message_key) DO UPDATE SET
message_text = EXCLUDED.message_text,
updated_at = NOW();

-- Ensure service query buttons exist
INSERT INTO eodb_service_query_buttons_final (button_text, query_template, button_order) VALUES
('Required documents', 'Required documents for {service_name}', 1),
('Timeline', 'Timeline for {service_name}', 2),
('Fees', 'Fees for {service_name}', 3)
ON CONFLICT (button_text) DO UPDATE SET
query_template = EXCLUDED.query_template,
button_order = EXCLUDED.button_order,
updated_at = NOW();

-- Verify the data
SELECT 'Service Query Messages:' as info;
SELECT message_key, message_text FROM eodb_greeting_messages_final 
WHERE message_key IN ('service_query_prompt', 'service_fallback_message', 'wrong_service_followup_message');

SELECT 'Service Query Buttons:' as info;
SELECT button_text, query_template, button_order FROM eodb_service_query_buttons_final ORDER BY button_order;
