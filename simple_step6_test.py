#!/usr/bin/env python3
"""
Simple test to verify Step 6 RAG fallback by directly testing with the session data from the logs.
"""

import requests
import json

def test_step6_with_existing_session():
    """Test Step 6 RAG fallback using the session ID from the logs"""
    
    # Use the session ID from the logs that we know has status_check_completed = True
    session_id = "3046096d-aa99-46db-ac77-71bafcf71a0d"
    
    # Test payload for Step 6 general query (matching the logs)
    payload = {
        "session_id": session_id,
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "user_input": "what is kanyashree?",  # Same query from the logs
        "step": 6,
        "response_type": "text"
    }
    
    print("Simple Step 6 RAG Fallback Test")
    print("=" * 50)
    print(f"Using session ID from logs: {session_id}")
    print(f"Query: '{payload['user_input']}'")
    print("Expected: Milvus fallback → RAG API call → Response with disclaimer")
    print("-" * 50)
    
    try:
        response = requests.post("http://localhost:8020/chatbot/step", json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            
            print("✅ Request successful!")
            print(f"Status code: {response.status_code}")
            print(f"Intent name: {result.get('intent_name', 'N/A')}")
            print(f"Response length: {len(response_text)} characters")
            print(f"Step returned: {result.get('step', 'N/A')}")
            
            # Check if RAG was used
            if 'Disclaimer: This is an AI generated Response' in response_text:
                print("\n✅ SUCCESS: RAG API was called! (Disclaimer found)")
                print("🎉 Step 6 RAG fallback is working correctly!")
                print(f"\nResponse preview:\n{response_text[:300]}...")
                return True
            elif 'Thank you for your query! Could you please clarify' in response_text:
                print("\n❌ FAILED: Still getting fallback message - RAG API not called")
                print(f"Full response:\n{response_text}")
                return False
            elif 'As on 04 March 2023' in response_text and len(response_text) > 100:
                print("\n✅ SUCCESS: RAG API likely called (formatted response with date)")
                print("🎉 Step 6 RAG fallback is working correctly!")
                print(f"\nResponse preview:\n{response_text[:300]}...")
                return True
            else:
                print("\n⚠️  UNCLEAR: Response doesn't match expected patterns")
                print(f"Full response:\n{response_text}")
                return False
                
        else:
            print(f"\n❌ FAILED: Request failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("\n⏰ TIMEOUT: Request took too long - RAG API might be processing")
        print("Check server logs to see if RAG API was called")
        return False
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        return False

def test_step6_with_new_session():
    """Test Step 6 with a fresh session ID"""
    
    import uuid
    session_id = str(uuid.uuid4())
    
    payload = {
        "session_id": session_id,
        "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
        "user_input": "what is digital india?",
        "step": 6,
        "response_type": "text"
    }
    
    print(f"\nTesting with new session: {session_id}")
    print(f"Query: '{payload['user_input']}'")
    print("-" * 50)
    
    try:
        response = requests.post("http://localhost:8020/chatbot/step", json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            
            print("✅ Request successful!")
            print(f"Intent name: {result.get('intent_name', 'N/A')}")
            print(f"Response length: {len(response_text)} characters")
            
            if 'Disclaimer: This is an AI generated Response' in response_text:
                print("✅ RAG API called successfully!")
                return True
            elif 'Thank you for your query! Could you please clarify' in response_text:
                print("❌ Still getting fallback message")
                return False
            else:
                print("⚠️  Unclear result")
                print(f"Response: {response_text[:200]}...")
                return None
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("Testing Step 6 RAG Fallback Fix")
    print("=" * 60)
    
    # Test with the session from logs first
    print("Test 1: Using session ID from logs")
    success1 = test_step6_with_existing_session()
    
    # Test with a new session
    print("\nTest 2: Using new session ID")
    success2 = test_step6_with_new_session()
    
    print("\n" + "=" * 60)
    print("FINAL RESULTS")
    print("=" * 60)
    
    if success1:
        print("✅ Test 1 (existing session): PASSED")
    else:
        print("❌ Test 1 (existing session): FAILED")
    
    if success2:
        print("✅ Test 2 (new session): PASSED")
    elif success2 is None:
        print("⚠️  Test 2 (new session): UNCLEAR")
    else:
        print("❌ Test 2 (new session): FAILED")
    
    if success1 or success2:
        print("\n🎉 Step 6 RAG fallback fix is working!")
    else:
        print("\n❌ Step 6 RAG fallback needs more investigation.")
        print("Check server logs for detailed information.")
