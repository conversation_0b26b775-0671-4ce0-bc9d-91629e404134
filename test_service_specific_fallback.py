#!/usr/bin/env python3
"""
Test script to verify the service-specific fallback functionality.
This tests the is_response_service_related function and the service fallback logic.
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_is_response_service_related():
    """Test the is_response_service_related function"""
    
    print("🧪 Testing is_response_service_related Function")
    print("=" * 60)
    
    try:
        # Import the function from main module
        from main import is_response_service_related
        
        # Test cases
        test_cases = [
            {
                "name": "Service-related response",
                "response": "To obtain consent to operate under the Air Prevention and Control of Pollution Act, you need to submit environmental clearance documents.",
                "selected_service": "Consent to Operate under the Air (Prevention and Control of Pollution) Act, 1981",
                "user_query": "what documents are required?",
                "expected": True
            },
            {
                "name": "General query about Kanyashree",
                "response": "Silpasathi is the Single Window Portal of the Government of West Bengal designed to facilitate businesses by providing statutory clearances licenses and approvals required to start and operate a business in the state.",
                "selected_service": "Consent to Operate under the Air (Prevention and Control of Pollution) Act, 1981",
                "user_query": "what is kanyashree?",
                "expected": False
            },
            {
                "name": "General query about Digital India",
                "response": "Silpasathi is the Single Window Portal of the Government of West Bengal designed to facilitate businesses by providing statutory clearances licenses and approvals required to start and operate a business in the state.",
                "selected_service": "Trade License",
                "user_query": "what is digital india?",
                "expected": False
            },
            {
                "name": "Service-related query about Trade License",
                "response": "Trade License is required for conducting business activities. You need to provide business registration documents and identity proof.",
                "selected_service": "Trade License",
                "user_query": "what documents needed for trade license?",
                "expected": True
            },
            {
                "name": "General query about Startup India",
                "response": "Online registration ensures: - Digital access to all services without physical visits. - Centralized tracking of approvals. - Reduced paperwork for businesses.",
                "selected_service": "Factory License",
                "user_query": "tell me about startup india",
                "expected": False
            }
        ]
        
        print("Running test cases...")
        print("-" * 40)
        
        passed = 0
        total = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            result = is_response_service_related(
                test_case["response"],
                test_case["selected_service"],
                test_case["user_query"]
            )
            
            status = "✅ PASS" if result == test_case["expected"] else "❌ FAIL"
            print(f"Test {i}: {test_case['name']}")
            print(f"  Query: '{test_case['user_query']}'")
            print(f"  Service: '{test_case['selected_service']}'")
            print(f"  Expected: {test_case['expected']}, Got: {result}")
            print(f"  Status: {status}")
            print()
            
            if result == test_case["expected"]:
                passed += 1
        
        print("=" * 60)
        print(f"TEST RESULTS: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! The function is working correctly.")
            return True
        else:
            print(f"❌ {total - passed} tests failed. The function needs adjustment.")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_service_fallback_message():
    """Test the service fallback message format"""
    
    print("\n🧪 Testing Service Fallback Message Format")
    print("=" * 60)
    
    try:
        from main import handle_service_query_fallback
        
        # Create a mock request object
        class MockRequest:
            def __init__(self, session_id):
                self.session_id = session_id
        
        mock_req = MockRequest("test_session")
        selected_service = "Consent to Operate under the Air (Prevention and Control of Pollution) Act, 1981"
        
        result = handle_service_query_fallback(mock_req, selected_service)
        
        print("Testing fallback message generation...")
        print("-" * 40)
        
        expected_parts = [
            f"This query is not related to <b>{selected_service}</b>",
            "If you want to ask general questions or other service releted quaries, type 'exit' or 'restart'",
            "Or you can continue asking questions about"
        ]
        
        response = result.get("response", "")
        print(f"Generated message:")
        print(f"'{response}'")
        print()
        
        all_parts_found = True
        for part in expected_parts:
            if part in response:
                print(f"✅ Found: '{part}'")
            else:
                print(f"❌ Missing: '{part}'")
                all_parts_found = False
        
        print()
        print("Checking response structure...")
        required_fields = ["session_id", "intent_id", "intent_name", "response", "response_type", "step"]
        
        structure_correct = True
        for field in required_fields:
            if field in result:
                print(f"✅ Field '{field}': {result[field]}")
            else:
                print(f"❌ Missing field: '{field}'")
                structure_correct = False
        
        print("=" * 60)
        if all_parts_found and structure_correct:
            print("🎉 Service fallback message test passed!")
            return True
        else:
            print("❌ Service fallback message test failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing fallback message: {e}")
        return False

def main():
    """Run all tests"""
    
    print("Service-Specific Fallback Test Suite")
    print("=" * 70)
    print("Testing the fix for service-specific query fallback functionality")
    print("=" * 70)
    
    # Test 1: Function logic
    test1_passed = test_is_response_service_related()
    
    # Test 2: Message format
    test2_passed = test_service_fallback_message()
    
    # Final summary
    print("\n" + "=" * 70)
    print("FINAL TEST RESULTS")
    print("=" * 70)
    
    print(f"✅ Function Logic Test: {'PASS' if test1_passed else 'FAIL'}")
    print(f"✅ Message Format Test: {'PASS' if test2_passed else 'FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Service-specific fallback functionality is working correctly")
        print("✅ Non-service queries will show the correct fallback message")
        print("✅ Service-related queries will show Milvus responses")
        
        print("\n📋 EXPECTED BEHAVIOR:")
        print("1. User selects a service (e.g., 'Air Pollution Consent')")
        print("2. User asks service-related question → Shows Milvus response")
        print("3. User asks general question (e.g., 'what is kanyashree?') → Shows fallback message")
        print("4. Fallback message directs user to type 'exit' or 'restart' for general queries")
        
    else:
        print("\n❌ SOME TESTS FAILED")
        print("The service-specific fallback functionality needs further investigation")
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
