#!/usr/bin/env python3
"""
Complete test for service-specific fallback functionality.
This test simulates the full user flow:
1. Select "Apply for licence/clearance"
2. Select application type (Pre-establishment/Pre-operational)
3. Select a specific service
4. Ask service-related questions (should get Milvus responses)
5. Ask general questions (should get fallback message)
"""

import requests
import json
import time

def test_service_specific_fallback():
    """Test the complete service-specific fallback flow"""
    
    print("🧪 Testing Service-Specific Fallback Functionality")
    print("=" * 70)
    print("This test simulates the user flow described in the issue:")
    print("1. Select 'Apply for licence/clearance'")
    print("2. Select application type")
    print("3. Select a service")
    print("4. Test service-related vs general queries")
    print("=" * 70)
    
    url = "http://localhost:8020/chatbot/step"
    session_id = "test_service_fallback"
    
    try:
        # Step 1: Select main option "Apply for licence/clearance"
        print("\n📋 Step 1: Selecting 'Apply for licence/clearance'")
        print("-" * 50)
        
        step1_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "step": 1,
            "user_response": {
                "caption": "main_option",
                "value": "1. Apply for licence/clearance"
            }
        }
        
        response1 = requests.post(url, json=step1_payload, timeout=30)
        if response1.status_code == 200:
            result1 = response1.json()
            print(f"✅ Step 1 successful: {response1.status_code}")
            print(f"Response: {result1.get('response', '')[:100]}...")
        else:
            print(f"❌ Step 1 failed: {response1.status_code}")
            return False
        
        time.sleep(1)
        
        # Step 2: Select application type "Pre-establishment"
        print("\n📋 Step 2: Selecting 'Pre-establishment'")
        print("-" * 50)
        
        step2_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "step": 2,
            "user_response": {
                "caption": "application_type",
                "value": "1. Pre-establishment"
            }
        }
        
        response2 = requests.post(url, json=step2_payload, timeout=30)
        if response2.status_code == 200:
            result2 = response2.json()
            print(f"✅ Step 2 successful: {response2.status_code}")
            print(f"Response: {result2.get('response', '')[:100]}...")
        else:
            print(f"❌ Step 2 failed: {response2.status_code}")
            return False
        
        time.sleep(1)
        
        # Step 3: Select a specific service
        print("\n📋 Step 3: Selecting a specific service")
        print("-" * 50)
        
        selected_service = "Consent to Operate under the Air (Prevention and Control of Pollution) Act, 1981"
        
        step3_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "step": 3,
            "user_response": {
                "caption": "selected_service",
                "value": selected_service
            }
        }
        
        response3 = requests.post(url, json=step3_payload, timeout=30)
        if response3.status_code == 200:
            result3 = response3.json()
            print(f"✅ Step 3 successful: {response3.status_code}")
            print(f"Selected service: {selected_service}")
            print(f"Response: {result3.get('response', '')[:100]}...")
        else:
            print(f"❌ Step 3 failed: {response3.status_code}")
            return False
        
        time.sleep(2)
        
        # Step 4: Test service-related query (should get Milvus response)
        print("\n📋 Step 4: Testing service-related query")
        print("-" * 50)
        
        service_query = "what documents are required?"
        
        step4_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "user_input": service_query,
            "step": 4,
            "response_type": "text"
        }
        
        print(f"Asking service-related question: '{service_query}'")
        response4 = requests.post(url, json=step4_payload, timeout=60)
        
        if response4.status_code == 200:
            result4 = response4.json()
            response_text4 = result4.get('response', '')
            intent_name4 = result4.get('intent_name', '')
            
            print(f"✅ Service query successful: {response4.status_code}")
            print(f"Intent: {intent_name4}")
            print(f"Response length: {len(response_text4)} chars")
            print(f"Response preview: {response_text4[:150]}...")
            
            # Check if it's a proper service response (not fallback message)
            if "This query is not related to" in response_text4:
                print("❌ Service query incorrectly triggered fallback!")
                service_query_success = False
            else:
                print("✅ Service query returned proper Milvus response")
                service_query_success = True
        else:
            print(f"❌ Service query failed: {response4.status_code}")
            service_query_success = False
        
        time.sleep(2)
        
        # Step 5: Test general query (should get fallback message)
        print("\n📋 Step 5: Testing general query (should trigger fallback)")
        print("-" * 50)
        
        general_query = "what is kanyashree?"
        
        step5_payload = {
            "session_id": session_id,
            "collection_name": "collection_b5e7c017_1a06_4857_819c_6a038133dd94",
            "user_input": general_query,
            "step": 4,
            "response_type": "text"
        }
        
        print(f"Asking general question: '{general_query}'")
        response5 = requests.post(url, json=step5_payload, timeout=60)
        
        if response5.status_code == 200:
            result5 = response5.json()
            response_text5 = result5.get('response', '')
            intent_name5 = result5.get('intent_name', '')
            
            print(f"✅ General query successful: {response5.status_code}")
            print(f"Intent: {intent_name5}")
            print(f"Response length: {len(response_text5)} chars")
            print(f"Response preview: {response_text5[:200]}...")
            
            # Check if it shows the correct fallback message
            expected_parts = [
                f"This query is not related to <b>{selected_service}</b>",
                "type 'exit' or 'restart'",
                "general query section"
            ]
            
            fallback_correct = all(part in response_text5 for part in expected_parts)
            
            if fallback_correct:
                print("✅ General query correctly triggered service fallback message!")
                general_query_success = True
            else:
                print("❌ General query did not show correct fallback message")
                print("Expected parts:")
                for part in expected_parts:
                    found = "✅" if part in response_text5 else "❌"
                    print(f"  {found} '{part}'")
                general_query_success = False
        else:
            print(f"❌ General query failed: {response5.status_code}")
            general_query_success = False
        
        # Final assessment
        print("\n" + "=" * 70)
        print("🎯 FINAL TEST RESULTS")
        print("=" * 70)
        
        print(f"✅ Service-related query: {'PASS' if service_query_success else 'FAIL'}")
        print(f"✅ General query fallback: {'PASS' if general_query_success else 'FAIL'}")
        
        overall_success = service_query_success and general_query_success
        
        if overall_success:
            print("\n🎉 SERVICE-SPECIFIC FALLBACK TEST: ✅ PASSED!")
            print("✅ Service-related queries return Milvus responses")
            print("✅ General queries show correct fallback message")
            print("✅ User's issue has been resolved!")
            
            print("\n📋 VERIFIED BEHAVIOR:")
            print("1. ✅ User selects service → Service context established")
            print("2. ✅ Service-related questions → Milvus responses shown")
            print("3. ✅ General questions → Fallback message shown")
            print("4. ✅ Fallback directs user to type 'exit'/'restart'")
            
        else:
            print("\n❌ SERVICE-SPECIFIC FALLBACK TEST: FAILED")
            if not service_query_success:
                print("❌ Service-related queries not working properly")
            if not general_query_success:
                print("❌ General query fallback not working properly")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Run the complete service fallback test"""
    
    print("Service-Specific Fallback Complete Test")
    print("=" * 70)
    print("Testing the fix for the user's specific issue:")
    print("- Service-related queries should show Milvus responses")
    print("- General queries should show fallback message (no RAG)")
    print("=" * 70)
    
    # Wait for server to be ready
    print("⏳ Waiting 3 seconds for server to be ready...")
    time.sleep(3)
    
    success = test_service_specific_fallback()
    
    if success:
        print("\n🎉 SUCCESS: The user's service-specific fallback issue has been resolved!")
    else:
        print("\n⚠️  ISSUE: Service-specific fallback needs further investigation")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
